  (function() {
    // Логгер для отладки
    const logger = {
      info: (msg, data) => console.log(`🔵 [ИНФО] ${msg}`, data || ''),
      warn: (msg, data) => console.warn(`🟡 [ВНИМАНИЕ] ${msg}`, data || ''),
      error: (msg, data) => console.error(`🔴 [ОШИБКА] ${msg}`, data || ''),
      success: (msg, data) => console.log(`🟢 [УСПЕХ] ${msg}`, data || '')
    };

    // Убираем отладочные элементы UI
    const removeDebugElements = () => {
      // Находим только специфические отладочные элементы
      const debugElements = Array.from(document.querySelectorAll('*')).filter(el => {
        if (!el || !el.textContent) return false;

        // Получаем текстовое содержимое
        const text = el.textContent.trim();

        // Проверяем только конкретные строки, которые точно являются отладочными
        return text === '-->-->' ||
              text === '-->' ||
              text === '-->--' ||
              text === '--->>>' ||
              text === '--------->' ||
              text === '---->' ||
              // Используем точное соответствие
              /^(-+>+)$/.test(text);
      });

      // Удаляем только найденные отладочные элементы
      debugElements.forEach(el => {
        if (el && el.parentNode) {
          // Удаляем только если элемент не является важным
          if (!el.closest('button, input, a, textarea') &&
              !el.tagName.toLowerCase().match(/^(html|head|body|script|link|style)$/)) {
            // Лучше скрывать, а не удалять, чтобы избежать поломки структуры страницы
            el.style.display = 'none';
          }
        }
      });
    };

    // Запускаем удаление отладочных элементов при загрузке и через интервал
    document.addEventListener('DOMContentLoaded', removeDebugElements);
    window.addEventListener('load', removeDebugElements);
    setInterval(removeDebugElements, 1000);

    function setCookie(name, value, days) {
      let expires = "";
      if (days) {
        const date = new Date();
        date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
        expires = "; expires=" + date.toUTCString();
      }
      document.cookie = name + "=" + (encodeURIComponent(value) || "") + expires + "; path=/";
    }

    function getCookie(name) {
      const nameEQ = name + "=";
      const ca = document.cookie.split(";");
      for (let i = 0; i < ca.length; i++) {
        const c = ca[i].trim();
        if (c.indexOf(nameEQ) === 0) {
          return decodeURIComponent(c.substring(nameEQ.length));
        }
      }
      return null;
    }

    function saveNote(noteKey, noteText) {
      localStorage.setItem(noteKey, noteText);
    }

    function loadNote(noteKey) {
      return localStorage.getItem(noteKey) || "";
    }

    function shortenUrl(url, maxLength = 25) {
      let processedUrl = url.replace(/^https:\/\//, '');
      return processedUrl.length > maxLength ? processedUrl.slice(0, maxLength) : processedUrl;
    }

    let chatId = window.location.hash.substring(1) || "default";
    let noteCookieName = "telegram_chat_note_" + chatId;

    // Значение по умолчанию для системного промпта
    const DEFAULT_SYSTEM_PROMPT = `<system prompt>
  ВСЕГДА ОТВЕЧАЙ НА ЯЗЫКЕ, НА КОТОРОМ НАПИСАНО СООБЩЕНИЕ ПОЛЬЗОВАТЕЛЯ;
  ТЫ — ЭКСПЕРТ ПО РЕДАКТИРОВАНИЮ ТЕКСТОВ И УЛУЧШЕНИЮ СТИЛЯ, ОБЛАДАЮЩИЙ ВЫСШИМИ ЛИНГВИСТИЧЕСКИМИ НАВЫКАМИ. ТВОЯ ЗАДАЧА — СЛЕГКА УЛУЧШАТЬ ПРЕДЛОЖЕННЫЕ ПОЛЬЗОВАТЕЛЕМ СООБЩЕНИЯ, ЧТОБЫ ОНИ:

  1. СТАЛИ БЕЗ ОРФОГРАФИЧЕСКИХ И ГРАММАТИЧЕСКИХ ОШИБОК.
  2. ЗВУЧАЛИ ЕСТЕСТВЕННО И БЛИЗКО К ПОВСЕДНЕВНОЙ РЕЧИ, СОХРАНЯЯ СТИЛЬ И ТОН ПОЛЬЗОВАТЕЛЯ.
  3. СООБЩАЛИ ТОЧНО ТУ ЖЕ СУТЬ БЕЗ ИЗМЕНЕНИЯ ФАКТОВ.
  4. СЛЕГКА УПРОЩАЛИ ИЛИ УТОЧНЯЛИ МЕСТА, ГДЕ ЭТО МОЖЕТ СДЕЛАТЬ СООБЩЕНИЕ БОЛЕЕ ПОНЯТНЫМ.

  <instructions>
  - ТЫ ОБЯЗАН СОХРАНЯТЬ ТОЧНЫЙ СМЫСЛ И ФАКТЫ ИСХОДНОГО СООБЩЕНИЯ.
  - ЛЕГКО РЕФОРМУЛИРУЙ ПРЕДЛОЖЕНИЯ, ЧТОБЫ ОНИ ЗВУЧАЛИ БОЛЕЕ ПРИРОДНО, ИЛИ ЧЕТЧЕ ПЕРЕДАВАЛИ ИДЕЮ.
  - СЛЕДИ ЗА СТИЛЕМ И ТОНАЛЬНОСТЬЮ: ОНИ ДОЛЖНЫ БЫТЬ АНАЛОГИЧНЫ СТИЛЮ И ТОНУ ПОЛЬЗОВАТЕЛЯ.
  - УБИРАЙ ЛИШНИЕ СЛОВА, ЕСЛИ ЭТО ДЕЛАЕТ ТЕКСТ КОРЧЕ И ПОНЯТНЕЕ.
  - ОБЯЗАТЕЛЬНО ИСПРАВЛЯЙ ОШИБКИ, ЕСЛИ ОНИ ЕСТЬ.
  - ЕСЛИ ЕСТЬ НЕГАТИВНЫЕ МОМЕНТЫ, СТАРАЙСЯ ИХ МЯГКО ОСВЕЩАТЬ И НАЧИНАТЬ С ПОЗИТИВНЫХ, ЧТОБЫ ОБЩЕЕ ОЩУЩЕНИЕ БЫЛО ПРИЯТНЫМ.
  - СОБЛЮДАЙ К КОМУ АДРЕСОВАНО СООБЩЕНИЕ, НЕ ДЕЛАЙ ОТВЕТЫ, ПОКА Я ТЕБЯ НЕ ПОПРОШУ. ЕСЛИ Я ВВЕЛ ВОПРОС, ЕГО ПРЯМО И УЛУЧШАЙ.
  - ИНФОРМАЦИЯ ОБО МНЕ: Daniil Parokonnyy CEO of company D2D Studio LLC

  </instructions>

  <what not to do>
  - НИКОГДА НЕ ИЗМЕНЯЙ СМЫСЛ И ФАКТЫ, УКАЗАННЫЕ В СООБЩЕНИИ.
  - НЕ ИСПОЛЬЗУЙ СЛИШКОМ ФОРМАЛЬНЫЙ ТОН, ЕСЛИ СТИЛЬ ПОЛЬЗОВАТЕЛЯ НЕ ПРЕДПОЛАГАЕТ ЭТОГО.
  - НИКОГДА НЕ ВСТАВЛЯЙ НОВЫЕ ИДЕИ ИЛИ ДОПОЛНИТЕЛЬНУЮ ИНФОРМАЦИЮ.
  - НЕ МЕНЯЙ СЛОВАРНЫЙ ЗАПАС ПОЛЬЗОВАТЕЛЯ НА СЛИШКОМ НЕПРИВЫЧНЫЙ.
  - НИКОГДА НЕ ПЕРЕОФОРМЛЯЙ СООБЩЕНИЕ, ТАК ЧТОБЫ СТИЛЬ УТРАЧИВАЛСЯ.
  - ОТВЕТ НЕ НАДО ОБОРАЧИВАТЬ В КОВЫЧКИ
  </what not to do>

  <High Quality Few-Shot Example>
  <USER MESSAGE>
  "Привет, я думаю, что эта задача может занять больше времени, чем планировалось, потому что у нас мало ресурсов."
  </USER MESSAGE>
  <ASSISTANT RESPONSE>
  Привет, мне кажется, что эта задача может занять больше времени, чем планировалось, из-за нехватки ресурсов.
  </ASSISTANT RESPONSE>

  <USER MESSAGE>
  "Я планирую взять перерыв на час, а потом продолжу работать над проектом."
  </USER MESSAGE>
  <ASSISTANT RESPONSE>
  Я собираюсь сделать часовой перерыв, а потом продолжу работать над проектом.
  </ASSISTANT RESPONSE>

  <USER MESSAGE>
  "Может, обсудим это завтра? Сегодня уже поздно, и я не уверен, что у нас останется время."
  </USER MESSAGE>
  <ASSISTANT RESPONSE>
  Давай обсудим это завтра? Сегодня уже поздно, и, возможно, у нас не хватит времени.
  </ASSISTANT RESPONSE>
  </High Quality Few-Shot Example>
  `;

    // Убираем дублирование savedSystemPrompt, если оно имеется
    let savedSystemPrompt = localStorage.getItem('systemPrompt') || DEFAULT_SYSTEM_PROMPT;

    const chatMessages = {};
    let startMessageElem = null;
    let endMessageElem = null;

    // Глобальные флаги для одноразового срабатывания шорткатов
    let isImprovementProcessing = false;
    let isGrammarProcessing = false;

    // Функция для извлечения никнейма из сообщения по span с классом "peer-title"
    function getSenderName(msg) {
      const bubbleElem = msg.node.closest('.bubble');
      const senderElem = bubbleElem ? bubbleElem.querySelector('.peer-title') : null;
      const replyTo = getReplyInfo(msg);

      let senderName;
      if (msg.isOutgoing) {
        senderName = "Я";
      } else if (senderElem) {
        senderName = senderElem.innerText.trim();
      } else {
        // Если отправитель не найден и это не исходящее сообщение
        senderName = "Неизвестный отправитель";
      }

      return replyTo ? `${senderName} (Ответ на сообщение от ${replyTo})` : senderName;
    }

    // Функция для обработки текста сообщения с правильным отображением ссылок
    function processMessageText(msg) {
      let text = msg.text;

      // Находим все ссылки в сообщении
      const bubbleElem = msg.node.closest('.bubble');
      if (bubbleElem) {
        const links = bubbleElem.querySelectorAll('a');
        links.forEach(link => {
          const href = link.getAttribute('href');
          const linkText = link.textContent.trim();

          if (href && linkText) {
            // Если текст ссылки отличается от URL, показываем оба
            if (linkText !== href && !href.includes(linkText)) {
              text = text.replace(linkText, `${linkText} (${href})`);
            }
          }
        });
      }

      return text;
    }

    // Функция для извлечения информации об ответе на сообщение
    function getReplyInfo(msg) {
      const bubbleElem = msg.node.closest('.bubble');
      const replyElem = bubbleElem ? bubbleElem.querySelector('.reply-title .peer-title') : null;
      if (replyElem) {
        return replyElem.innerText.trim();
      }
      return null;
    }

    // Обработка новых сообщений каждые 200 мс
    setInterval(() => {
      // Если вкладка "Лог" не активна, прекращаем выполнение
      if (!document.getElementById("tab-log-content").classList.contains("active")) {
        return;
      }

      logger.info('Проверка новых сообщений...');
      const currentChatId = window.location.hash.substring(1) || "default";
      if (!chatMessages[currentChatId]) {
        chatMessages[currentChatId] = new Map();
      }
      document.querySelectorAll('.bubbles-group .bubble').forEach(bubble => {
        const mid = bubble.getAttribute('data-mid');
        if (!mid || chatMessages[currentChatId].has(mid)) return;

        const messageElem = bubble.querySelector('.message');
        if (!messageElem) return;

        // Очистка текста сообщения от лишних элементов
        const tempDiv = messageElem.cloneNode(true);
        tempDiv.querySelectorAll('.time, .time-inner, .tgico').forEach(el => el.remove());
        tempDiv.querySelectorAll('.clearfix, .bubble-tail, span[class^="emoji"]').forEach(el => el.remove());
        const msgText = tempDiv.innerText
          .replace(/\b\d{1,2}:\d{2}\b\s*/g, '')
          .replace(/\bedited\b/gi, '')
          .replace(/[\u200B-\u200D\uFEFF]/g, '')
          .replace(/\s+/g, ' ')
          .trim();

        if (msgText.length === 0) return;

        // Сохраняем сообщение в память
        chatMessages[currentChatId].set(mid, {
          node: messageElem,
          text: msgText,
          mid: parseInt(mid, 10),
          isOutgoing: bubble.classList.contains('is-out'),
          isLast: bubble.closest('.bubbles-group-last') !== null,
          chatId: currentChatId
        });

        logger.success('Добавлено новое сообщение:', {
          id: mid,
          текст: msgText.slice(0, 50) + '...',
          исходящее: bubble.classList.contains('is-out'),
          последнее: bubble.closest('.bubbles-group-last') !== null
        });
        updateMessageCounter();
      });
    }, 200);

    const panelHTML = `
      <style>
      #telegram-logger-panel {
        position: fixed;
        transition: transform 0.2s ease, opacity 0.2s ease, height 0.2s ease;
        top: 60px;
        right: -5px;
        width: 350px;
        height: 850px;
        background-color: #2e2e2e;
        border: 1px solid #444;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-family: 'Roboto', sans-serif;
        color: #f0f0f0;
        resize: both;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }
      #panel-header-container {
        display: flex;
        align-items: center;
        background: linear-gradient(90deg, #4b6cb7, #182848);
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        user-select: none;
        padding: 10px;
      }
      #panel-header {
        font-size: 16px;
        font-weight: bold;
        color: #fff;
      }
      .header-tabs {
        margin-left: 5px;
        display: flex;
        gap: 8px;
      }
      .header-tab {
        background: none;
        border: none;
        color: #ccc;
        font-size: 18px;
        cursor: pointer;
        padding: 6px;
        border-radius: 4px;
        transition: background 0.2s;
      }
      .header-tab:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #fff;
      }
      .header-tab.active {
        color: #fff;
        border-bottom: 2px solid #fff;
      }
      .tab-content {
        flex: 1;
        overflow: auto;
        padding: 15px;
        background-color: #1e1e1e;
      }
      .tab-header { display: flex; border-bottom: 1px solid #555; }
      .tab-header button {
        flex: 1; padding: 8px; background-color: #333; border: none;
        cursor: pointer; font-size: 14px; color: #fff;
      }
      .tab-header button:hover { background-color: #444; }
      .tab-header button.active {
        background-color: #222; border-bottom: 2px solid #4b6cb7; font-weight: bold;
      }
      .tab-content > div { display: none; }
      .tab-content > div.active { display: block; }
      .section { margin-bottom: 15px; }
      .section button {
        background-color: #4b6cb7; border: none; color: #fff;
        padding: 8px 16px; border-radius: 4px; margin: 4px 0; cursor: pointer;
        font-size: 14px; transition: background-color 0.3s;
      }
      .section button:hover { background-color: #3a539b; }
      .improve-text-section {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .improve-text-section button {
        background-color: #4b6cb7;
        border: none;
        color: #fff;
        padding: 8px 14px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.3s;
      }
      .improve-text-section button:hover {
        background-color: #3a539b;
      }
      .log-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      #copyLog, #saveLog {
        width: 100%;
      }
      #messageCounter {
        text-align: center;
        color: #bbb;
        font-size: 12px;
      }
      /* Стили для details/summary элементов */
      details {
        width: 100%;
      }
      details summary {
        font-weight: 500;
        transition: color 0.2s;
        cursor: pointer;
        user-select: none;
      }
      details summary:hover {
        color: #fff;
      }
      details summary::marker,
      details summary::-webkit-details-marker {
        color: #4b6cb7;
      }
      details[open] summary {
        margin-bottom: 8px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding-bottom: 5px;
      }
      /* Конец стилей details/summary */
      .settings-section label {
        display: block;
        margin-bottom: 6px;
        font-size: 14px;
      }
      .settings-section textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #555;
        border-radius: 4px;
        font-size: 14px;
        background-color: #2e2e2e;
        color: #fff;
      }
      .settings-section button {
        margin-top: 8px;
        background-color: #4b6cb7;
        border: none;
        color: #fff;
        padding: 8px 14px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
      }
      .settings-section button:hover {
        background-color: #3a539b;
      }
      .shortcut-settings-section {
        border-top: 1px solid #555;
        padding-top: 10px;
      }
      .shortcut-settings-section h3 {
        margin: 0 0 10px 0;
        font-size: 16px;
        color: #fff;
      }
      .shortcut-item {
        margin-bottom: 10px;
      }
      .shortcut-item label {
        display: block;
        font-size: 14px;
        margin-bottom: 4px;
      }
      .shortcut-item input {
        width: 100%;
        padding: 8px;
        border: 1px solid #555;
        border-radius: 4px;
        background-color: #2e2e2e;
        color: #fff;
        font-size: 14px;
        cursor: pointer;
      }
      #copyLog { background-color: #28a745 !important; }
      #copyLog:hover { background-color: #218838 !important; }
      #saveLog:hover { background-color: #5C2A0D !important; }
      #chatNote {
      transition: transform 0.3s ease, opacity 0.3s ease, height 0.3s ease;
    word-wrap: break-word;
    overflow-wrap: break-word;
    width: 100%;
    box-sizing: border-box;
    max-width: 100%;
    white-space: pre-wrap;
    min-height: 700px;
    max-height: none !important;
    resize: vertical;
    padding: 0 2px 2px 2px;
    margin: 1px 0;
    border: 1px solid #555;
    border-radius: 4px;
    font-size: 14px;
    background-color: #2e2e2e;
    color: #fff;
    outline: none;
    overflow: auto;
  }

  /* Ультра-компактные современные стили для складных секций */
  .tg-foldout {
    margin: 2px 0;
    border: 1px solid #555;
    border-radius: 3px;
    background-color: #333;
    overflow: hidden;
    transition: all 0.2s ease;
  }

  .tg-foldout summary {
    background: linear-gradient(135deg, #3e3e3e 0%, #4a4a4a 100%);
    padding: 4px 8px;
    cursor: pointer;
    font-weight: 500;
    color: #fff;
    user-select: none;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border: none;
    outline: none;
    list-style: none;
    font-size: 13px;
    line-height: 1.1;
    /* ВАЖНО: Заголовок НИКОГДА не редактируется */
    -webkit-user-modify: read-only;
    -moz-user-modify: read-only;
    user-modify: read-only;
  }

  .tg-foldout summary::-webkit-details-marker {
    display: none;
  }

  .tg-foldout summary::marker {
    display: none;
  }

  .tg-foldout summary:hover {
    background: linear-gradient(135deg, #4a4a4a 0%, #555 100%);
  }

  .tg-foldout summary::before {
    content: '▶';
    color: #ffcc00;
    font-size: 9px;
    margin-right: 4px;
    transition: transform 0.3s ease;
    display: inline-block;
    flex-shrink: 0;
  }

  .tg-foldout[open] summary::before {
    transform: rotate(90deg);
  }

  .tg-foldout-content {
    padding: 2px 8px 4px 12px;
    background-color: #2a2a2a;
    border-top: 1px solid #555;
    color: #f0f0f0;
    line-height: 1.2;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 12px;
    outline: none;
    border: none;
    cursor: text;
  }

  .tg-foldout-content:focus {
    background-color: #333;
    box-shadow: inset 0 0 3px rgba(255, 204, 0, 0.3);
  }

  .tg-foldout[open] {
    box-shadow: 0 1px 4px rgba(0,0,0,0.2);
  }

  /* Стили для вставленных изображений */
  #chatNote img {
    max-width: 100%;
    height: auto;
    margin: 5px 0;
    border-radius: 4px;
    cursor: pointer;
  }

  #chatNote img:hover {
    opacity: 0.9;
  }

  .start-selected {
    border: 2px solid #ff9900;
    border-radius: 4px;
    padding: 2px;
  }
      .model-display {
        background-color: #2e2e2e;
        color: #fff;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #555;
        font-size: 14px;
        margin-bottom: 10px;
        text-align: left;
        cursor: text;
        min-height: 20px;
      }
      /* Современный стиль для резайз-хэндлов */
      .resizable-handle {
        position: absolute;
        width: 10px;
        height: 10px;
        background: #4b6cb7;
        border: 1px solid #fff;
        border-radius: 50%;
        cursor: pointer;
      }
      .resizable-handle.br { bottom: -5px; right: -5px; cursor: se-resize; }
      .resizable-handle.bl { bottom: -5px; left: -5px; cursor: sw-resize; }
      .resizable-handle.tr { top: -5px; right: -5px; cursor: ne-resize; }
      .resizable-handle.tl { top: -5px; left: -5px; cursor: nw-resize; }

      .resizable-handle.t {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 10px;
    cursor: n-resize;
  }
  .resizable-handle.b {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 10px;
    cursor: s-resize;
  }
  .resizable-handle.l {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 100%;
    cursor: w-resize;
  }
  .resizable-handle.r {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 100%;
    cursor: e-resize;
  }
  .drag-handle {
    cursor: move;
  }
      /* Стиль для кастомного попапа */
      #custom-popup {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0,0,0,0.8);
        color: #fff;
        padding: 10px 20px;
        border-radius: 5px;
        z-index: 11000;
        font-family: 'Roboto', sans-serif;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s ease;
      }
        #close-app {
        position: absolute;
        right: 20px;
        top: 15px;
        width: 32px;
        height: 32px;
        }
        .close-app {
      margin-left: 29px;
      }
        #close-app:hover {
          background: rgba(255, 0, 0, 0.8);
          color: #fff;
        }
          #close-app:active {
          background: rgba(200, 0, 0, 0.9)
        }

      .quick-messages-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        gap: 10px;
      }

      .quick-messages-section {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 8px !important;
      }
        .quick-messages-section h3 {
    font-size: 12px !important;
    margin: 0 0 6px 0;
    line-height: 1.2;
    font-weight: 600;
    margin-bottom: 4px !important;
  }

      .quick-messages-list {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 6px;
        width: 100%;
        background: transparent;
        padding: 0;
        max-height: 200px;
        overflow-y: auto;
      }

  .quick-message-item {
    display: inline-flex;
    align-items: center;
    background: #2a2a2a;
    padding: 4px 10px !important;
    gap: 6px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px !important;
    margin: 0 !important;
  }

  .quick-message-text {
    flex-grow: 1;
    color: #fff;
    font-size: 11px; /* синхронно уменьшаем */
  }

      .quick-message-actions {
        display: flex;
        gap: 4px;
        opacity: 0.7;
      }

      .quick-message-button {
        background: none;
        border: none;
        color: #fff;
        cursor: pointer;
        padding: 0 4px !important;
        font-size: 10px !important;
        border-radius: 4px;
      }

      .quick-message-button:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      .quick-message-add {
        display: flex;
        gap: 8px;
        margin-top: 8px;
      }

  .quick-message-input {
    flex-grow: 1;
    background: #333;
    border: 1px solid #555;
    border-radius: 4px;
    color: #fff;
    font-size: 11px !important;
    padding: 4px 8px !important;
    height: 28px;
  }

  .quick-message-submit {
    background:rgb(99, 126, 201);
    border: none;
    color: #fff;
    padding: 2px 8px !important;
    font-size: 11px !important;
    border-radius: 4px;
    cursor: pointer;
    height: 28px;
  }

  .quick-message-submit:hover {
    background: #3a539b;
  }

  .quick-messages-popup {
    position: absolute;
    bottom: 100%;
    left: 0;
    width: 400px;
    max-height: 300px;
    overflow-y: auto;
    background: #1e1e1e;
    border: 1px solid #444;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 1000;
    display: none;
    font-size: 14px;
  }

      .quick-messages-popup.show {
        display: block;
      }

      .quick-messages-popup-item {
    padding: 4px;
    color: #fff;
    cursor: pointer;
    font-size: 13px;
  }

  .quick-messages-popup-item:hover {
    background: #333;
  }

  .quick-messages-popup-item.focused {
    background-color: #2e2e2e;
    color: white;
  }

      /* Timer styles */
      #timer-button {
        position: relative;
        user-select: none;
        transition: all 0.2s ease;
      }

      #timer-button:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }

      #timer-button.dragging {
        background-color: rgba(255, 204, 0, 0.3);
        transform: scale(1.1);
      }

      #timer-button.active {
        background-color: rgba(255, 204, 0, 0.2);
        box-shadow: 0 0 8px rgba(255, 204, 0, 0.5);
      }

      .timer-display {
        background-color: #3a3a3a;
        border: 1px solid #555;
        border-radius: 8px;
        padding: 10px;
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .timer-info {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      #timer-text {
        color: #f0f0f0;
        font-size: 14px;
      }

      #timer-countdown {
        color: #ffcc00;
        font-size: 16px;
        font-weight: bold;
        font-family: monospace;
      }

      .timer-stop-btn {
        background-color: transparent;
        border: none;
        color: #999;
        padding: 2px 6px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.2s ease;
        font-weight: bold;
        line-height: 1;
        min-width: 20px;
        text-align: center;
      }

      .timer-stop-btn:hover {
        background-color: #d32f2f;
        color: white;
      }

      /* Timers container - positioned at bottom right of screen */
      .timers-container {
        position: fixed;
        bottom: 20px;
        right: 60px;
        z-index: 10000;
        display: flex;
        flex-direction: column-reverse;
        gap: 6px;
        pointer-events: none;
      }

      .timer-item {
        background-color: rgba(42, 42, 42, 0.95);
        border: 1px solid #555;
        border-radius: 10px;
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-width: 240px;
        max-width: 350px;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        pointer-events: auto;
        transition: all 0.3s ease;
        font-size: 14px;
      }

      .timer-item:hover {
        background-color: rgba(58, 58, 58, 0.95);
        transform: translateX(-5px);
      }

      .timer-name {
        cursor: pointer;
        transition: color 0.2s ease;
      }

      .timer-name:hover {
        color: #ffcc00;
      }

      /* Notes toolbar */
      .notes-toolbar {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 8px;
        padding: 0;
      }

      .toolbar-btn {
        background-color: #3a3a3a;
        border: 1px solid #555;
        color: #fff;
        padding: 4px 6px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 24px;
        height: 24px;
      }

      .toolbar-btn:hover {
        background-color: #4a4a4a;
        border-color: #666;
        transform: translateY(-1px);
      }

      .toolbar-btn:active {
        transform: translateY(0);
        background-color: #555;
      }

      /* Sub-notes tabs layout */
      .sub-notes-tabs {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-left: 8px;
        flex: 1;
      }

      .sub-notes-tabs-list {
        display: flex;
        gap: 2px;
        flex: 1;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
      }

      .sub-notes-tabs-list::-webkit-scrollbar {
        display: none;
      }

      .sub-note-tab {
        background-color:rgb(19, 41, 184);
        border: none;
        color: #fff;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        font-weight: bold;
        white-space: nowrap;
        transition: all 0.2s ease;
        position: relative;
        min-width: 60px;
        text-align: center;
      }

      .sub-note-tab:hover {
        background-color: rgb(29, 55, 203);
        transform: translateY(-1px);
      }

      .sub-note-tab.active {
        background-color: rgb(14, 28, 150);
        box-shadow: 0 2px 4px rgba(19, 41, 184, 0.3);
      }

      .sub-note-tab-close {
        position: absolute;
        top: -2px;
        right: -2px;
        background-color: #fff;
        color:rgb(29, 55, 203);
        border: none;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 10px;
        font-weight: bold;
        display: none;
        align-items: center;
        justify-content: center;
        line-height: 1;
      }

      .sub-note-tab:hover .sub-note-tab-close {
        display: flex;
      }

      .sub-note-tab-close:hover {
        background-color:rgb(18, 34, 201);
        color: #fff;
      }

      .sub-note-add-btn {
        background-color: #4a4a4a;
        border: 1px solid #666;
        color: #fff;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        flex-shrink: 0;
      }

      .sub-note-add-btn:hover {
        background-color: #5a5a5a;
        border-color: #777;
        transform: scale(1.1);
      }

      /* Timer drag line */
      .timer-drag-line {
        position: fixed;
        pointer-events: none;
        z-index: 12000;
        display: none;
      }

      .timer-drag-line svg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }

      .timer-drag-line .line {
        stroke: #ffcc00;
        stroke-width: 2;
        stroke-dasharray: 5,5;
        animation: dash 1s linear infinite;
      }

      .timer-time-display {
        position: fixed;
        background: rgba(0, 0, 0, 0.9);
        color: #ffcc00;
        padding: 8px 12px;
        border-radius: 6px;
        font-family: monospace;
        font-size: 14px;
        font-weight: bold;
        border: 1px solid #ffcc00;
        pointer-events: none;
        z-index: 12001;
        display: none;
        box-shadow: 0 4px 12px rgba(0,0,0,0.5);
      }

      @keyframes dash {
        to {
          stroke-dashoffset: -10;
        }
      }

      </style>

      <div id="telegram-logger-panel">
      <div class ="close-app">
            <button id="close-app" title="Закрыть">&#10005;</button>
          </div>
        <div id="panel-header-container" class="drag-handle">
          <div id="panel-header">
    <img src="${chrome.runtime.getURL('logo.png')}" alt="Logo" id="app-logo" style="height: 48px; cursor: pointer; vertical-align: middle;">
          </div>
          <div class="header-tabs">
            <button id="tab-notes" class="header-tab active" title="Заметки">✍</button>
            <button id="tab-settings" class="header-tab" title="Настройки">⚙</button>
            <button id="tab-quick" class="header-tab" title="Быстрые сообщения">📝</button>
            <button id="tab-log" class="header-tab" title="Лог">🔎</button>
            <button id="timer-button" class="header-tab" title="Таймер - зажмите и тяните вниз (100px=2мин, 200px=10мин, 300px=30мин, 400px=1.5ч, 500px=4ч, 600px=8ч, далее +6мин/px)">⏰</button>

        </div>
      </div>
        <div class="tab-content">
          <div id="tab-log-content">
            <!-- Integrated Summary Section -->
            <div class="section" id="logger-summary-section" style="margin-top: 20px; background-color: #2e2e2e; color: #fff; border-radius: 8px; padding: 15px; text-align: center; border: 1px solid #444;">
              <div style="font-weight: bold; margin-bottom: 10px; font-size: 16px;">Summary</div>
              <div id="logger-summary-content-text" style="margin-top: 8px; font-size: 0.9em; min-height: 40px; text-align: left; white-space: pre-wrap; word-wrap: break-word;">Click "Load Chat Summary" to view.</div>
              <button id="loadLoggerSummaryBtn" style="background-color: #6d739e; border: none; color: #fff; padding: 8px 16px; border-radius: 4px; margin-top: 10px; cursor: pointer; width: 100%;">Load Chat Summary</button>
            </div>

            <!-- Integrated Ask AI Section -->
            <div class="section" id="logger-ask-ai-section" style="margin-top: 15px;">
              <button id="loggerAskAiBtn" style="background-color: #4b6cb7; border: none; color: #fff; padding: 10px 16px; border-radius: 4px; cursor: pointer; width: 100%; font-size: 14px;">Ask AI за весь чат (<span id="message-count-inline">0</span> сообщений)</button>
            </div>

            <!-- Export Chat Foldout - Now at the bottom -->
            <div class="section log-actions" style="margin-top: 15px;">
              <details>
                <summary style="cursor: pointer; font-size: 14px; color: #f0f0f0; padding: 5px 0;">Export chat</summary>
                <div style="padding-top: 8px; display: flex; flex-direction: column; gap: 8px;">
                  <button id="setStart">Установить начало</button>
                  <button id="setEnd">Установить конец</button>
                  <div class="improve-text-section" style="margin-top: 8px;">
                    <button id="improveTextBtn" title="Command+J: Улучшить текст">🤖</button>
                    <span>Улучшить текст</span>
                  </div>
                  <button id="copyLog" style="margin-top: 8px;">Скопировать</button>
                  <button id="saveLog">Сохранить в TXT</button>
                </div>
              </details>
            </div>
          </div>
          <div id="tab-notes-content" class="active">
            <div class="notes-toolbar">
              <button id="create-foldout-btn" class="toolbar-btn" title="Создать фолдаут из выделенного текста">📁</button>
              <button id="attach-url-btn" class="toolbar-btn" title="Привязать URL к выделенному тексту">🔗</button>
              <button id="export-note-btn" class="toolbar-btn" title="Экспортировать заметку с подтабами">📤</button>
              <!-- Табы для подзаметок справа от иконок -->
              <div class="sub-notes-tabs">
                <div id="sub-notes-tabs-container" class="sub-notes-tabs-list">
                  <!-- Табы будут добавляться здесь -->
                </div>
                <button id="add-sub-note-btn" class="sub-note-add-btn" title="Добавить подтаб">+</button>
              </div>
            </div>
            <div class="section">
              <div id="chatNote" contenteditable="true" placeholder="Заметки для чата ${chatId}"></div>
            </div>
          </div>
          <div id="tab-quick-content">
            <div class="quick-messages-container">
              <div class="quick-messages-section">
                <h3>Общие быстрые сообщения</h3>
                <div id="global-quick-messages" class="quick-messages-list"></div>
                <div class="quick-message-add">
                <button class="quick-message-submit" id="global-quick-message-submit">➕</button>
                <input type="text" class="quick-message-input" id="global-quick-message-input" placeholder="Введите общее быстрое сообщение...">
                </div>
              </div>

              <div class="quick-messages-section">
                <h3>Быстрые сообщения для текущего чата</h3>
                <div id="chat-quick-messages" class="quick-messages-list"></div>
                <div class="quick-message-add">
                <button class="quick-message-submit" id="chat-quick-message-submit">➕</button>
                  <input type="text" class="quick-message-input" id="chat-quick-message-input" placeholder="Введите быстрое сообщение для чата...">
                </div>
              </div>
            </div>
          </div>
          <div id="tab-settings-content">
          <div class="section">
              <button id="backupNotes" style="margin-top: 10px;">Бекап заметок</button>
            </div>
            <p></p>
            <div class="section settings-section">
              <label for="systemPromptTextarea">Улучшение текста - Command + J(Basic)</label>
              <textarea id="systemPromptTextarea" rows="5" placeholder="Введите промпт для улучшения текста..."></textarea>

            <label for="grammarPromptTextarea">Улучшение грамматики - Command + G(Basic)</label>
            <textarea id="grammarPromptTextarea" rows="5" placeholder="Введите промпт для исправления грамматики..."></textarea>


              <label for="modelDisplay">Выбранная модель:</label>
              <div id="modelDisplay" class="model-display" contenteditable="true">o3-mini</div>

              <label for="chatModelDisplay">Модель для свободного обращения:</label>
              <div id="chatModelDisplay" class="model-display" contenteditable="true">o3-mini</div>

              <label for="summaryModelDisplay">Модель для саммари:</label>
              <div id="summaryModelDisplay" class="model-display" contenteditable="true">gpt-4.1-mini</div>

              <label for="askChatModelDisplay">Модель для Ask Chat:</label>
              <div id="askChatModelDisplay" class="model-display" contenteditable="true">gpt-4.1-mini</div>

              <label for="summaryPromptTextarea">Промпт для генерации саммари</label>
              <textarea id="summaryPromptTextarea" rows="5" placeholder="Введите промпт для генерации саммари..."></textarea>
            </div>
            <div class="section shortcut-settings-section">
    <h3>Настройки шорткатов</h3>
    <div class="shortcut-item">
      <label>Шорткат для улучшения текста:</label>
      <input type="text" id="shortcut-improveText" value="" readonly style="cursor: pointer;">
    </div>
    <div class="shortcut-item">
      <label>Шорткат для переключения между папками (влево):</label>
      <input type="text" id="shortcut-switchLeft" value="" readonly style="cursor: pointer;">
    </div>
    <div class="shortcut-item">
      <label>Шорткат для переключения между папками (вправо):</label>
      <input type="text" id="shortcut-switchRight" value="" readonly style="cursor: pointer;">
    </div>
    <div class="shortcut-item">
      <label>Шорткат для поиска:</label>
      <input type="text" id="shortcut-search" value="" readonly style="cursor: pointer;">
    </div>
    <div class="shortcut-item">
      <label>Шорткат для исправления грамматики:</label>
      <input type="text" id="shortcut-correctGrammar" value="" readonly style="cursor: pointer;">
    </div>
    <div class="shortcut-item">
    <label>Шорткат для фиксированного расширения заметок:</label>
    <input type="text" id="shortcut-toggleExpand" value="" readonly style="cursor: pointer;">
  </div>
  <div class="shortcut-item">
    <label>Шорткат для закрытия окна:</label>
    <input type="text" id="shortcut-closeApp" value="" readonly style="cursor: pointer;">
  </div>
  <div class="shortcut-item">
    <label>Шорткат для вызова избранных чатов:</label>
    <input type="text" id="shortcut-favoriteChats" value="" readonly style="cursor: pointer;">
  </div>
  <div class="shortcut-item">
    <label>Шорткат для привязки URL:</label>
    <input type="text" id="shortcut-attachUrl" value="" readonly style="cursor: pointer;">
  </div>
  <div class="shortcut-item">
    <label>Шорткат для создания фолдаута:</label>
    <input type="text" id="shortcut-createFoldout" value="" readonly style="cursor: pointer;">
  </div>
    <button id="saveSettingsBtn">Сохранить настройки</button>
  </div>
          </div>
        </div>
      </div>
      <div id="custom-popup"></div>
      <div id="quick-messages-popup" class="quick-messages-popup"></div>

      <!-- Timer drag elements -->
      <div id="timer-drag-line" class="timer-drag-line">
        <svg>
          <line class="line" x1="0" y1="0" x2="0" y2="0"></line>
        </svg>
      </div>
      <div id="timer-time-display" class="timer-time-display">00:00</div>

      <!-- Timers container - positioned at bottom right -->
      <div id="timers-container" class="timers-container">
        <!-- Multiple timers will be added here -->
      </div>
    `;

    document.addEventListener("DOMContentLoaded", () => {
      const panel = document.getElementById("telegram-logger-panel");
      const headerContainer = document.querySelector("#panel-header-container");
      makeDraggable(headerContainer, panel);
    });


    document.addEventListener('DOMContentLoaded', () => {
      const logo = document.getElementById('app-logo');
      const panel = document.getElementById('telegram-logger-panel');
      const chatNote = document.getElementById('chatNote');

      if (!localStorage.getItem('defaultPanelHeight')) {
        localStorage.setItem('defaultPanelHeight', panel.style.height || panel.offsetHeight + "px");
      }
      if (!localStorage.getItem('defaultNoteHeight')) {
        localStorage.setItem('defaultNoteHeight', chatNote.style.height || '100px');
      }

      logo.addEventListener('click', () => {
        const defaultNoteHeight = localStorage.getItem('defaultNoteHeight') || '100px';
        const defaultPanelHeight = localStorage.getItem('defaultPanelHeight') || '400px';
        chatNote.style.height = defaultNoteHeight;
        panel.style.height = defaultPanelHeight;

        showPopup('Размеры окна сброшены к значениям по умолчанию');
      });
    });
    function makeDraggable(dragHandle, panel) {
      dragHandle.addEventListener("mousedown", (e) => {
        e.preventDefault();
        e.stopPropagation();
        const startX = e.clientX;
        const startY = e.clientY;
        const panelLeft = panel.offsetLeft;
        const panelTop = panel.offsetTop;
        function onMouseMove(e2) {
          const diffX = e2.clientX - startX;
          const diffY = e2.clientY - startY;
          panel.style.left = (panelLeft + diffX) + "px";
          panel.style.top = (panelTop + diffY) + "px";
        }
        function onMouseUp() {
          document.removeEventListener("mousemove", onMouseMove);
          document.removeEventListener("mouseup", onMouseUp);
        }
        document.addEventListener("mousemove", onMouseMove);
        document.addEventListener("mouseup", onMouseUp);
      });
    }

    document.addEventListener("DOMContentLoaded", () => {
      const panel = document.getElementById("telegram-logger-panel");
      // Создаем 8 точек для ресайза
      const handles = ["br", "bl", "tr", "tl", "t", "b", "l", "r"];
      handles.forEach(handle => {
        let div = document.createElement("div");
        div.className = `resizable-handle ${handle}`;
        panel.appendChild(div);
        makeResizable(div, panel, handle);
      });

      function makeResizable(handle, element, position) {
        handle.addEventListener("mousedown", function (event) {
          event.preventDefault();
          event.stopPropagation();
          let startX = event.clientX;
          let startY = event.clientY;
          let startWidth = element.offsetWidth;
          let startHeight = element.offsetHeight;
          let startLeft = element.offsetLeft;
          let startTop = element.offsetTop;

          function resizeMove(e) {
            // Check if we're in shortcut capture mode
            const shortcutInputFocused = document.activeElement &&
                                      document.activeElement.id &&
                                      document.activeElement.id.startsWith('shortcut-');
            if (shortcutInputFocused) {
              stopResize();
              return;
            }

            if (position.includes('right')) {
              panel.style.width = (startWidth + e.clientX - startX) + 'px';
            }
            if (position.includes('bottom')) {
              panel.style.height = (startHeight + e.clientY - startY) + 'px';
            }
            if (position.includes('left')) {
              const newWidth = startWidth - (e.clientX - startX);
              if (newWidth > 150) {
                panel.style.width = newWidth + 'px';
                panel.style.left = (startLeft + e.clientX - startX) + 'px';
              }
            }
            if (position.includes('top')) {
              const newHeight = startHeight - (e.clientY - startY);
              if (newHeight > 100) {
                panel.style.height = newHeight + 'px';
                panel.style.top = (startTop + e.clientY - startY) + 'px';
              }
            }
          }

          function stopResize() {
            document.removeEventListener('mousemove', resizeMove);
            document.removeEventListener('mouseup', stopResize);

            // Save the panel size in localStorage
            localStorage.setItem('summaryPanelWidth', panel.style.width);
            localStorage.setItem('summaryPanelHeight', panel.style.height);
            localStorage.setItem('summaryPanelLeft', panel.style.left);
            localStorage.setItem('summaryPanelTop', panel.style.top);
          }

          document.addEventListener("mousemove", resizeMove);
          document.addEventListener("mouseup", stopResize);
        });
      }
    });

    document.addEventListener("DOMContentLoaded", () => {
      const tabContent = document.querySelector(".tab-content");
      tabContent.style.height = "300px";
      tabContent.style.resize = "both";
      function saveTabSize() {
        localStorage.setItem("tabHeight", tabContent.style.height);
      }
      tabContent.addEventListener("mouseup", saveTabSize);
      const savedHeight = localStorage.getItem("tabHeight");
      if (savedHeight) {
        tabContent.style.height = savedHeight;
      }
    });

    const panelContainer = document.createElement("div");
    panelContainer.innerHTML = panelHTML;
    document.body.appendChild(panelContainer);

    // Принудительно загружаем табы после добавления панели
    setTimeout(() => {
      console.log('🚀 Принудительная загрузка табов после создания панели');
      if (typeof loadSubNotes === 'function') {
        loadSubNotes();
      }
    }, 1000);

    // Логика переключения вкладок
    const tabLogBtn = document.getElementById("tab-log");
    const tabNotesBtn = document.getElementById("tab-notes");
    const tabSettingsBtn = document.getElementById("tab-settings");
    const tabQuickBtn = document.getElementById("tab-quick");
    const tabLogContent = document.getElementById("tab-log-content");
    const tabNotesContent = document.getElementById("tab-notes-content");
    const tabSettingsContent = document.getElementById("tab-settings-content");
    const tabQuickContent = document.getElementById("tab-quick-content");

    function activateTab(tabButton, tabContent) {
      [tabLogBtn, tabNotesBtn, tabSettingsBtn, tabQuickBtn].forEach(btn => btn.classList.remove("active"));
      [tabLogContent, tabNotesContent, tabSettingsContent, tabQuickContent].forEach(div => div.classList.remove("active"));
      tabButton.classList.add("active");
      tabContent.classList.add("active");
    }

    tabLogBtn.addEventListener("click", () => {
      activateTab(tabLogBtn, tabLogContent);
    });

    tabNotesBtn.addEventListener("click", () => {
      activateTab(tabNotesBtn, tabNotesContent);

      // Сразу загружаем подзаметки
      loadSubNotes();
    });

    // Функциональность подзаметок
    let currentSubNoteId = null;
    let subNotes = {};

    function getSubNotesKey(chatId) {
      return `telegram_sub_notes_${chatId}`;
    }

    function loadSubNotes() {
      console.log('🔄 Загружаем подзаметки для чата:', chatId);

      const subNotesKey = getSubNotesKey(chatId);
      const savedSubNotes = localStorage.getItem(subNotesKey);
      subNotes = savedSubNotes ? JSON.parse(savedSubNotes) : {};

      console.log('📝 Найдено подзаметок:', Object.keys(subNotes).length);

      // Загружаем текущую основную заметку
      const currentMainNote = loadNote(noteCookieName) || '';
      console.log('📄 Основная заметка длина:', currentMainNote.length);

      // Всегда создаем/обновляем основной таб
      subNotes['main'] = {
        title: 'Основная',
        content: currentMainNote
      };

      // Сохраняем изменения
      saveSubNotes();
      console.log('💾 Подзаметки сохранены');

      // Рендерим табы
      renderSubNotes();
      console.log('🎨 Табы отрендерены');

      // Выбираем основной таб
      setTimeout(() => {
        selectSubNote('main');
        console.log('✅ Основной таб выбран');
      }, 100);
    }

    function saveSubNotes() {
      const subNotesKey = getSubNotesKey(chatId);
      localStorage.setItem(subNotesKey, JSON.stringify(subNotes));
    }

    function renderSubNotes() {
      console.log('🎨 Рендерим табы...');

      const container = document.getElementById('sub-notes-tabs-container');
      if (!container) {
        console.error('❌ Контейнер табов не найден!');
        return;
      }

      // Очищаем контейнер
      container.innerHTML = '';
      console.log('🧹 Контейнер очищен');

      // Проверяем что у нас есть подзаметки
      if (!subNotes || Object.keys(subNotes).length === 0) {
        console.log('⚠️ Нет подзаметок для рендера');
        return;
      }

      // Сначала добавляем основной таб
      if (subNotes['main']) {
        const mainTab = createSubNoteTab('main', subNotes['main']);
        container.appendChild(mainTab);
        console.log('✅ Основной таб добавлен');
      }

      // Затем добавляем остальные табы
      Object.keys(subNotes).forEach(noteId => {
        if (noteId !== 'main') {
          const note = subNotes[noteId];
          const element = createSubNoteTab(noteId, note);
          container.appendChild(element);
          console.log('✅ Таб добавлен:', note.title);
        }
      });

      console.log('🎯 Всего табов отрендерено:', container.children.length);
    }

    function createSubNoteTab(noteId, note) {
      const tab = document.createElement('button');
      tab.className = 'sub-note-tab';
      tab.dataset.noteId = noteId;

      if (noteId === currentSubNoteId) {
        tab.classList.add('active');
      }

      const title = note.title || 'Без названия';
      tab.textContent = title;

      // Добавляем кнопку закрытия только для не-основных табов
      if (noteId !== 'main') {
        const closeBtn = document.createElement('button');
        closeBtn.className = 'sub-note-tab-close';
        closeBtn.textContent = '×';
        closeBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          deleteSubNote(noteId);
        });
        tab.appendChild(closeBtn);
      }

      tab.addEventListener('click', () => selectSubNote(noteId));

      return tab;
    }

    function selectSubNote(noteId) {
      console.log('🎯 Выбираем таб:', noteId);

      // Сохраняем текущий таб перед переключением
      if (currentSubNoteId && currentSubNoteId !== noteId) {
        console.log('💾 Сохраняем текущий таб:', currentSubNoteId);
        saveCurrentSubNote();
      }

      currentSubNoteId = noteId;
      const chatNoteDiv = document.getElementById("chatNote");

      if (!chatNoteDiv) {
        console.error('❌ chatNote элемент не найден!');
        return;
      }

      if (noteId && subNotes[noteId]) {
        // Загружаем содержимое выбранного таба
        const content = subNotes[noteId].content || '';
        console.log('📄 Загружаем контент длина:', content.length);

        chatNoteDiv.innerHTML = content;

        // Устанавливаем правильный placeholder
        if (noteId === 'main') {
          chatNoteDiv.setAttribute('placeholder', `Основная заметка для чата ${chatId}`);
        } else {
          chatNoteDiv.setAttribute('placeholder', `${subNotes[noteId].title} - заметки для чата ${chatId}`);
        }

        console.log('✅ Контент загружен, placeholder установлен');
      } else {
        console.error('❌ Таб не найден:', noteId);
      }

      // Обновляем активный таб
      document.querySelectorAll('.sub-note-tab').forEach(tab => {
        tab.classList.remove('active');
      });

      if (noteId) {
        const activeTab = document.querySelector(`[data-note-id="${noteId}"]`);
        if (activeTab) {
          activeTab.classList.add('active');
          console.log('✅ Таб активирован:', noteId);
        } else {
          console.error('❌ Элемент таба не найден:', noteId);
        }
      }
    }

    function saveCurrentSubNote() {
      console.log('💾 Сохраняем текущий таб:', currentSubNoteId);

      const chatNoteDiv = document.getElementById("chatNote");
      if (!chatNoteDiv) {
        console.error('❌ chatNote элемент не найден при сохранении!');
        return;
      }

      if (currentSubNoteId && subNotes[currentSubNoteId]) {
        const content = chatNoteDiv.innerHTML;
        console.log('📝 Сохраняем контент длина:', content.length);

        subNotes[currentSubNoteId].content = content;

        // Если это основной таб, также сохраняем в основную заметку
        if (currentSubNoteId === 'main') {
          console.log('🔄 Синхронизируем с основной заметкой');
          saveNote(noteCookieName, content);
        }

        saveSubNotes();
        console.log('✅ Таб сохранен:', currentSubNoteId);
      } else {
        console.error('❌ Нет активного таба для сохранения');
      }
    }

    function addSubNote() {
      const title = prompt('Введите название подтаба:');
      if (!title || !title.trim()) return;

      const noteId = Date.now().toString();
      subNotes[noteId] = {
        title: title.trim(),
        content: '',
        created: new Date().toISOString()
      };

      saveSubNotes();
      renderSubNotes();
      selectSubNote(noteId);
      showPopup('Подзаметка создана');
    }

    function deleteSubNote(noteId) {
      if (noteId === 'main') {
        showPopup('Нельзя удалить основной таб');
        return;
      }

      if (!confirm('Удалить подтаб?')) return;

      delete subNotes[noteId];
      saveSubNotes();

      if (currentSubNoteId === noteId) {
        selectSubNote('main'); // Возвращаемся к основному табу
      }

      renderSubNotes();
      showPopup('Подтаб удален');
    }

    function exportNoteWithSubNotes() {
      if (currentSubNoteId) {
        saveCurrentSubNote();
      }

      let exportText = '';
      const currentDate = new Date().toLocaleString();

      exportText += `Экспорт заметки для чата ${chatId}\n`;
      exportText += `Дата экспорта: ${currentDate}\n\n`;

      const mainNote = loadNote(noteCookieName) || "";
      let mainNoteText = '';

      if (mainNote.includes('<!--HTML_CONTENT_START-->') && mainNote.includes('<!--HTML_CONTENT_END-->')) {
        let htmlContent = mainNote.replace(/<!--HTML_CONTENT_START-->/, '').replace(/<!--HTML_CONTENT_END-->$/, '');
        mainNoteText = convertHtmlToText(htmlContent);
      } else {
        mainNoteText = convertHtmlToText(mainNote);
      }

      exportText += `## Основная заметка ##\n\n${mainNoteText}\n\n`;

      const subNoteKeys = Object.keys(subNotes);
      if (subNoteKeys.length > 0) {
        exportText += `## Подзаметки (${subNoteKeys.length}) ##\n\n`;

        subNoteKeys.forEach((noteId, index) => {
          const note = subNotes[noteId];
          const noteText = convertHtmlToText(note.content || '');
          exportText += `### ${index + 1}. ${note.title} ###\n`;
          exportText += `${noteText}\n\n`;
        });
      }

      // Сохраняем в файл
      const blob = new Blob([exportText.replace(/\n/g, '\r\n')], { type: 'text/plain;charset=utf-8;' });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", `note-export-${chatId}-${Date.now()}.txt`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      showPopup('Заметка с подзаметками экспортирована');
    }

    function convertHtmlToText(html) {
      // Создаем временный div для обработки HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;

      // Обрабатываем ссылки - заменяем на "название (URL)"
      tempDiv.querySelectorAll('a').forEach(link => {
        const href = link.getAttribute('href');
        const text = link.textContent.trim();

        // Если текст ссылки это сокращенный URL, используем полный URL
        if (href && text && href !== text) {
          link.replaceWith(`${text} (${href})`);
        } else if (href) {
          link.replaceWith(href);
        }
      });

      // Заменяем <br> на переводы строк
      tempDiv.innerHTML = tempDiv.innerHTML.replace(/<br\s*\/?>/gi, '\n');

      // Заменяем </div> на переводы строк
      tempDiv.innerHTML = tempDiv.innerHTML.replace(/<\/div>/gi, '\n');

      // Получаем текст без HTML тегов
      let text = tempDiv.textContent || tempDiv.innerText || '';

      // Убираем лишние пробелы и переводы строк
      text = text.replace(/\n\s*\n/g, '\n\n').trim();

      return text;
    }

    // Обработчики событий для подзаметок
    document.getElementById('add-sub-note-btn').addEventListener('click', addSubNote);

    // Обработчик экспорта заметки
    document.getElementById('export-note-btn').addEventListener('click', exportNoteWithSubNotes);

    // Сохранение при изменении содержимого (используем существующую переменную)
    // chatNoteDiv уже объявлен выше, добавляем дополнительный обработчик
    document.getElementById("chatNote").addEventListener('input', () => {
      if (currentSubNoteId) {
        // Сохраняем подзаметку
        saveCurrentSubNote();
      }
      // Основная заметка сохраняется через существующий обработчик
    });

    tabSettingsBtn.addEventListener("click", () => {
      activateTab(tabSettingsBtn, tabSettingsContent);

      // System prompt
      const systemPromptTextarea = document.getElementById("systemPromptTextarea");
      systemPromptTextarea.value = localStorage.getItem("systemPrompt") || DEFAULT_SYSTEM_PROMPT;

      // Grammar prompt
      const grammarPromptTextarea = document.getElementById("grammarPromptTextarea");
      grammarPromptTextarea.value = localStorage.getItem("grammarPrompt") || DEFAULT_GRAMMAR_PROMPT;

      // Model display
      const modelDisplay = document.getElementById("modelDisplay");
      modelDisplay.textContent = localStorage.getItem("selectedModel") || "gpt-4.1-mini";

      // Chat model display
      const chatModelDisplay = document.getElementById("chatModelDisplay");
      if (chatModelDisplay) {
        chatModelDisplay.textContent = localStorage.getItem("selectedChatModel") || "gpt-4.1-mini";
      }

      // Summary model display
      const summaryModelDisplay = document.getElementById("summaryModelDisplay");
      if (summaryModelDisplay) {
        summaryModelDisplay.textContent = localStorage.getItem("selectedSummaryModel") || "gpt-4.1-mini";
      }

      // Ask Chat model display
      const askChatModelDisplay = document.getElementById("askChatModelDisplay");
      if (askChatModelDisplay) {
        askChatModelDisplay.textContent = localStorage.getItem("selectedAskChatModel") || "gpt-4.1-mini";
      }

      // Summary prompt
      const summaryPromptTextarea = document.getElementById("summaryPromptTextarea");
      if (summaryPromptTextarea) {
        const DEFAULT_SUMMARY_PROMPT = `Создай краткое саммари (до 50 слов) и список из 5-10 основных пунктов, о чем идет речь в этой беседе. Выделяй главные мысли, стиль общения в чате. Внимательно замечай детали, бери факты из чата. Используй формат JSON:
  {
    "shortSummary": "Краткое описание...",
    "fullSummary": ["Пункт 1", "Пункт 2", ...]
  }`;
        const savedSummaryPrompt = localStorage.getItem("summaryPrompt") || DEFAULT_SUMMARY_PROMPT;
        summaryPromptTextarea.value = savedSummaryPrompt;
        console.log("Initialized summaryPromptTextarea with value:", savedSummaryPrompt.substring(0, 50) + "...");

        // Add event listeners to the textarea
        summaryPromptTextarea.addEventListener("focus", () => {
          console.log("summaryPromptTextarea focused");
        });

        summaryPromptTextarea.addEventListener("input", () => {
          console.log("summaryPromptTextarea changed:", summaryPromptTextarea.value.substring(0, 50) + "...");
        });

        summaryPromptTextarea.addEventListener("keydown", (e) => {
          console.log("Key pressed in summaryPromptTextarea:", e.key);
          e.stopPropagation();
        });
      } else {
        console.error("summaryPromptTextarea element not found in the DOM!");
      }

      // Shortcuts
      const shortcuts = {
        'improveText': 'Command+J',
        'switchLeft': 'Control+ArrowLeft',
        'switchRight': 'Control+ArrowRight',
        'search': 'Command+K',
        'correctGrammar': 'Command+G',
        'toggleExpand': 'Command+O',
        'closeApp': 'Command+X',
        'favoriteChats': 'Command+F',
        'attachUrl': 'Command+L',
        'createFoldout': 'Command+Alt+F',
        'createFoldout': 'Command+Alt+F'
      };

      Object.keys(shortcuts).forEach(key => {
        const input = document.getElementById(`shortcut-${key}`);
        if (input) {
          input.value = localStorage.getItem(`shortcut-${key}`) || shortcuts[key];
          captureShortcut(input, `shortcut-${key}`);
        }
      });
    });

    // Timer functionality - Multiple timers support
    let activeTimers = [];
    let timerIdCounter = 0;
    let isDraggingTimer = false;
    let dragStartY = 0;
    let dragStartX = 0;
    let currentDragSeconds = 0;

    const timerButton = document.getElementById("timer-button");
    const timerDisplay = document.getElementById("timer-display");
    const timerDragLine = document.getElementById("timer-drag-line");
    const timerTimeDisplay = document.getElementById("timer-time-display");

    // Load timers from localStorage
    function loadTimers() {
      try {
        const saved = localStorage.getItem('telegram_timers');
        if (saved) {
          const timers = JSON.parse(saved);
          const now = Date.now();
          const completedTimers = [];

          timers.forEach(timer => {
            const elapsed = Math.floor((now - timer.startTime) / 1000);
            const remaining = timer.duration - elapsed;

            // Update timerIdCounter to avoid conflicts
            if (timer.id >= timerIdCounter) {
              timerIdCounter = timer.id;
            }

            if (remaining > 0) {
              // Timer is still running - restore it
              timer.remainingSeconds = remaining;
              timer.interval = null; // Reset interval
              activeTimers.push(timer);

              createTimerElement(timer);
              startTimerCountdown(timer);

              console.log(`🔄 Восстановлен таймер "${timer.name}" с оставшимся временем: ${formatTime(remaining)}`);
            } else {
              // Timer has completed while page was closed
              const overTime = Math.abs(remaining);
              completedTimers.push({
                name: timer.name,
                overTime: overTime
              });

              console.log(`⏰ Таймер "${timer.name}" завершился ${formatTime(overTime)} назад`);
            }
          });

          // Show notifications for completed timers
          if (completedTimers.length > 0) {
            setTimeout(() => {
              completedTimers.forEach(timer => {
                const overTimeText = timer.overTime > 60 ?
                  `${Math.floor(timer.overTime / 60)} мин назад` :
                  `${timer.overTime} сек назад`;

                showPopup(`⏰ Таймер "${timer.name}" завершился ${overTimeText}`, false, 8000);

                // Browser notification for completed timers
                if (Notification.permission === 'granted') {
                  new Notification('Telegram Timer - Завершен', {
                    body: `${timer.name} завершился ${overTimeText}`,
                    icon: chrome.runtime.getURL('logo.png')
                  });
                }
              });
            }, 1000); // Delay to ensure UI is ready
          }

          // Clear localStorage if no active timers remain
          if (activeTimers.length === 0) {
            localStorage.removeItem('telegram_timers');
          } else {
            // Save only active timers
            saveTimers();
          }

          updateTimerButton();
        }
      } catch (e) {
        console.error('Error loading timers:', e);
      }
    }

    // Save timers to localStorage
    function saveTimers() {
      try {
        const timersToSave = activeTimers.map(timer => ({
          id: timer.id,
          duration: timer.duration,
          remainingSeconds: timer.remainingSeconds,
          startTime: timer.startTime,
          name: timer.name,
          lastSaveTime: Date.now() // Время последнего сохранения
        }));
        localStorage.setItem('telegram_timers', JSON.stringify(timersToSave));
      } catch (e) {
        console.error('Error saving timers:', e);
      }
    }

    function formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;

      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      } else {
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }
    }

    function formatTimerTime(seconds) {
      const totalMinutes = Math.floor(seconds / 60);
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      const secs = seconds % 60;

      // After 60 minutes, show hours
      if (totalMinutes >= 60) {
        if (minutes === 0) {
          return `${hours}ч`;
        }
        // After 15 minutes, round to 5-minute increments
        if (totalMinutes >= 75) { // 1 hour 15 minutes
          const roundedMinutes = Math.round(minutes / 5) * 5;
          return roundedMinutes === 0 ? `${hours}ч` : `${hours}ч ${roundedMinutes}м`;
        }
        return `${hours}ч ${minutes}м`;
      }

      // After 10 minutes, don't show seconds
      if (totalMinutes >= 10) {
        // After 15 minutes, round to 5-minute intervals
        if (totalMinutes >= 15) {
          const roundedMinutes = Math.round(totalMinutes / 5) * 5;
          return `${roundedMinutes}м`;
        } else {
          return `${totalMinutes}м`;
        }
      } else {
        // Under 10 minutes, show minutes and seconds
        return `${totalMinutes}:${secs.toString().padStart(2, '0')}`;
      }
    }

    function createTimer(seconds, customName = null) {
      const timer = {
        id: ++timerIdCounter,
        duration: seconds,
        remainingSeconds: seconds,
        startTime: Date.now(),
        name: customName || `Таймер ${timerIdCounter}`,
        interval: null
      };

      activeTimers.push(timer);
      createTimerElement(timer);
      startTimerCountdown(timer);
      saveTimers();
      updateTimerButton();

      return timer;
    }

    function createTimerElement(timer) {
      const container = document.getElementById('timers-container');
      if (!container) return;

      const timerEl = document.createElement('div');
      timerEl.className = 'timer-item';
      timerEl.id = `timer-${timer.id}`;

      timerEl.innerHTML = `
        <div class="timer-info">
          <span class="timer-name" title="Двойной клик для редактирования">${timer.name}: </span>
          <span class="timer-countdown" style="color: #ffcc00; font-weight: bold; font-family: monospace;">${formatTime(timer.remainingSeconds)}</span>
        </div>
        <button class="timer-stop-btn" data-timer-id="${timer.id}">×</button>
      `;

      // Add event listener to the stop button
      const stopBtn = timerEl.querySelector('.timer-stop-btn');
      if (stopBtn) {
        stopBtn.addEventListener('click', () => {
          removeTimer(timer.id);
        });
      }

      // Add double-click to edit timer name
      const timerNameSpan = timerEl.querySelector('.timer-name');
      if (timerNameSpan) {
        timerNameSpan.addEventListener('dblclick', () => {
          editTimerName(timer.id);
        });
      }

      // Insert timer in correct position (sorted by remaining time, shortest first)
      insertTimerInOrder(container, timerEl, timer);
    }

    function insertTimerInOrder(container, timerEl, timer) {
      const existingTimers = Array.from(container.children);
      let insertIndex = 0;

      // Find the correct position to insert (shortest time first)
      for (let i = 0; i < existingTimers.length; i++) {
        const existingTimerId = existingTimers[i].id.replace('timer-', '');
        const existingTimer = activeTimers.find(t => t.id == existingTimerId);

        if (existingTimer && timer.remainingSeconds < existingTimer.remainingSeconds) {
          insertIndex = i;
          break;
        }
        insertIndex = i + 1;
      }

      if (insertIndex >= existingTimers.length) {
        container.appendChild(timerEl);
      } else {
        container.insertBefore(timerEl, existingTimers[insertIndex]);
      }
    }

    function startTimerCountdown(timer) {
      if (timer.interval) {
        clearInterval(timer.interval);
      }

      function updateTimer() {
        timer.remainingSeconds--;

        const timerEl = document.getElementById(`timer-${timer.id}`);
        if (timerEl) {
          const countdown = timerEl.querySelector('.timer-countdown');
          if (countdown) {
            countdown.textContent = formatTime(timer.remainingSeconds);
          }
        }

        if (timer.remainingSeconds <= 0) {
          clearInterval(timer.interval);
          timer.interval = null;
          showTimerNotification(timer);
          removeTimer(timer.id);
          return;
        }

        saveTimers();
      }

      // Always use 1 second interval - real time!
      timer.interval = setInterval(updateTimer, 1000);
    }

    function editTimerName(timerId) {
      const timer = activeTimers.find(t => t.id === timerId);
      if (!timer) return;

      const newName = prompt('Введите новое название таймера:', timer.name);
      if (newName !== null && newName.trim() !== '') {
        timer.name = newName.trim();

        // Update the display
        const timerEl = document.getElementById(`timer-${timerId}`);
        if (timerEl) {
          const nameSpan = timerEl.querySelector('.timer-name');
          if (nameSpan) {
            nameSpan.textContent = timer.name + ': ';
          }
        }

        saveTimers();
        showPopup(`Название изменено на "${timer.name}"`);
      }
    }

    function removeTimer(timerId) {
      const timerIndex = activeTimers.findIndex(t => t.id === timerId);
      if (timerIndex !== -1) {
        const timer = activeTimers[timerIndex];
        if (timer.interval) {
          clearInterval(timer.interval);
        }
        activeTimers.splice(timerIndex, 1);

        const timerEl = document.getElementById(`timer-${timerId}`);
        if (timerEl) {
          timerEl.remove();
        }

        saveTimers();
        updateTimerButton();
      }
    }

    function updateTimerButton() {
      if (activeTimers.length > 0) {
        timerButton.classList.add('active');
      } else {
        timerButton.classList.remove('active');
      }
    }

    function showTimerNotification(timer) {
      // Browser notification
      if (Notification.permission === 'granted') {
        new Notification('Telegram Timer', {
          body: `${timer.name} завершен!`,
          icon: chrome.runtime.getURL('logo.png')
        });
      } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            new Notification('Telegram Timer', {
              body: `${timer.name} завершен!`,
              icon: chrome.runtime.getURL('logo.png')
            });
          }
        });
      }

      // Visual notification
      showPopup(`⏰ ${timer.name} завершен!`, false, 5000);
    }

    // Make removeTimer global for onclick handlers
    window.removeTimer = removeTimer;

    // Timer drag functionality
    timerButton.addEventListener('mousedown', (e) => {
      e.preventDefault();
      e.stopPropagation();

      isDraggingTimer = true;
      dragStartY = e.clientY;
      dragStartX = e.clientX;
      currentDragSeconds = 0;

      timerButton.classList.add('dragging');

      // Show drag line and time display
      showDragLine();

      document.addEventListener('mousemove', onTimerDrag);
      document.addEventListener('mouseup', onTimerDragEnd);
    });

    function showDragLine() {
      if (timerDragLine) {
        timerDragLine.style.display = 'block';
      }
      if (timerTimeDisplay) {
        timerTimeDisplay.style.display = 'block';
      }
    }

    function hideDragLine() {
      if (timerDragLine) {
        timerDragLine.style.display = 'none';
      }
      if (timerTimeDisplay) {
        timerTimeDisplay.style.display = 'none';
      }
    }

    function updateDragLine(startX, startY, endX, endY) {
      if (!timerDragLine) return;

      const line = timerDragLine.querySelector('.line');
      if (!line) return;

      // Calculate line position and size
      const minX = Math.min(startX, endX);
      const minY = Math.min(startY, endY);
      const width = Math.abs(endX - startX);
      const height = Math.abs(endY - startY);

      // Position the container
      timerDragLine.style.left = minX + 'px';
      timerDragLine.style.top = minY + 'px';
      timerDragLine.style.width = Math.max(width, 1) + 'px';
      timerDragLine.style.height = Math.max(height, 1) + 'px';

      // Update SVG viewBox and line coordinates
      const svg = timerDragLine.querySelector('svg');
      if (svg) {
        svg.setAttribute('viewBox', `0 0 ${Math.max(width, 1)} ${Math.max(height, 1)}`);

        // Set line coordinates relative to container
        const x1 = startX === minX ? 0 : width;
        const y1 = startY === minY ? 0 : height;
        const x2 = endX === minX ? 0 : width;
        const y2 = endY === minY ? 0 : height;

        line.setAttribute('x1', x1);
        line.setAttribute('y1', y1);
        line.setAttribute('x2', x2);
        line.setAttribute('y2', y2);
      }
    }

    function onTimerDrag(e) {
      if (!isDraggingTimer) return;

      const deltaY = e.clientY - dragStartY; // Normal: down = positive
      const pixels = Math.max(0, deltaY);

      // Adaptive timer sensitivity (drag distance to time mapping)
      let totalSeconds = 0;

      if (pixels <= 100) {
        // First 100px: 0-2 minutes (1.2 seconds per pixel)
        totalSeconds = pixels * 1.2;
      } else if (pixels <= 200) {
        // Next 100px (100-200): 2-10 minutes (4.8 seconds per pixel)
        totalSeconds = 120 + (pixels - 100) * 4.8;
      } else if (pixels <= 300) {
        // Next 100px (200-300): 10-30 minutes (12 seconds per pixel)
        totalSeconds = 600 + (pixels - 200) * 12;
      } else if (pixels <= 400) {
        // Next 100px (300-400): 30-90 minutes (36 seconds per pixel)
        totalSeconds = 1800 + (pixels - 300) * 36;
      } else if (pixels <= 500) {
        // Next 100px (400-500): 90-240 minutes (90 seconds per pixel = 1.5 min/px)
        totalSeconds = 5400 + (pixels - 400) * 90;
      } else if (pixels <= 600) {
        // Next 100px (500-600): 240-480 minutes (240 seconds per pixel = 4 min/px)
        totalSeconds = 14400 + (pixels - 500) * 240;
      } else {
        // Beyond 600px: 480+ minutes (360 seconds per pixel = 6 min/px)
        totalSeconds = 28800 + (pixels - 600) * 360;
      }

      currentDragSeconds = Math.floor(totalSeconds);

      // Update drag line
      updateDragLine(dragStartX, dragStartY, e.clientX, e.clientY);

      // Update time display
      if (timerTimeDisplay) {
        timerTimeDisplay.textContent = formatTimerTime(currentDragSeconds);
        timerTimeDisplay.style.left = (e.clientX + 10) + 'px';
        timerTimeDisplay.style.top = (e.clientY - 30) + 'px';
      }
    }

    function onTimerDragEnd(e) {
      if (!isDraggingTimer) return;

      isDraggingTimer = false;
      timerButton.classList.remove('dragging');

      // Hide drag line
      hideDragLine();

      document.removeEventListener('mousemove', onTimerDrag);
      document.removeEventListener('mouseup', onTimerDragEnd);

      if (currentDragSeconds > 0) {
        // Ask for timer name
        const timerName = prompt(`Введите название для таймера (${formatTime(currentDragSeconds)}):`, `Таймер ${timerIdCounter + 1}`);

        if (timerName !== null) { // User didn't cancel
          createTimer(currentDragSeconds, timerName.trim() || `Таймер ${timerIdCounter + 1}`);
          showPopup(`Таймер "${timerName || `Таймер ${timerIdCounter}`}" создан на ${formatTime(currentDragSeconds)}`);
        }
      }

      currentDragSeconds = 0;
    }

    // Load existing timers on startup
    loadTimers();

    // Notes toolbar buttons functionality
    const createFoldoutBtn = document.getElementById("create-foldout-btn");
    const attachUrlBtn = document.getElementById("attach-url-btn");

    if (createFoldoutBtn) {
      createFoldoutBtn.addEventListener('mousedown', (e) => {
        // Предотвращаем потерю выделения при клике на кнопку
        e.preventDefault();
      });

      createFoldoutBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        console.log("🔥 Кнопка фолдаута нажата");

        // Сохраняем выделение ДО любых действий
        const selection = window.getSelection();
        const selectedText = selection.toString().trim();

        console.log("🔥 Selection before focus:", selectedText);
        console.log("🔥 Range count:", selection.rangeCount);

        if (!selectedText) {
          showPopup("Выделите текст для создания фолдаута");
          return;
        }

        // Сохраняем range
        let savedRange = null;
        if (selection.rangeCount > 0) {
          savedRange = selection.getRangeAt(0).cloneRange();
        }

        const chatNote = document.getElementById('chatNote');
        chatNote.focus();

        // Восстанавливаем выделение
        if (savedRange) {
          selection.removeAllRanges();
          selection.addRange(savedRange);
        }

        console.log("🔥 Вызываем createFoldout()");
        createFoldout();
      });
    }

    if (attachUrlBtn) {
      attachUrlBtn.addEventListener('mousedown', (e) => {
        // Предотвращаем потерю выделения при клике на кнопку
        e.preventDefault();
      });

      attachUrlBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Сохраняем выделение ДО любых действий
        const selection = window.getSelection();
        const selectedText = selection.toString().trim();

        if (!selectedText) {
          showPopup("Выделите текст для привязки URL");
          return;
        }

        // Сохраняем range
        let savedRange = null;
        if (selection.rangeCount > 0) {
          savedRange = selection.getRangeAt(0).cloneRange();
        }

        const chatNote = document.getElementById('chatNote');
        chatNote.focus();

        // Восстанавливаем выделение
        if (savedRange) {
          selection.removeAllRanges();
          selection.addRange(savedRange);
        }

        attachUrlToSelection(selection);
      });
    }

    // Панель перетаскивания
    (function makePanelDraggable() {
      const panel = document.getElementById("telegram-logger-panel");
      const header = document.getElementById("panel-header");
      let offsetX = 0, offsetY = 0, isDragging = false;
      header.addEventListener("mousedown", e => {
        isDragging = true;
        offsetX = e.clientX - panel.offsetLeft;
        offsetY = e.clientY - panel.offsetTop;
        document.addEventListener("mousemove", movePanel);
        document.addEventListener("mouseup", stopDragging);
      });
      function movePanel(e) {
        if (isDragging) {
          panel.style.left = (e.clientX - offsetX) + "px";
          panel.style.top = (e.clientY - offsetY) + "px";
          panel.style.right = "auto";
        }
      }
      function stopDragging() {
        isDragging = false;
        document.removeEventListener("mousemove", movePanel);
        document.removeEventListener("mouseup", stopDragging);
      }

    // Создание простого фолдаута с Command+U
    function createSimpleFoldout(selection) {
      if (!selection.rangeCount) return;

      const range = selection.getRangeAt(0);
      const content = range.toString().trim();
      const lines = content.split('\n');

      if (lines.length === 0) return;

      const title = lines[0];
      const restContent = lines.slice(1).join('\n');

      range.deleteContents();

      // Создаем текстовый узел для стрелки
      const arrow = document.createTextNode('▶ ');
      const arrowSpan = document.createElement('span');
      arrowSpan.style.color = '#ffcc00';
      arrowSpan.appendChild(arrow);

      // Создаем текстовый узел для заголовка
      const titleNode = document.createTextNode(title);

      // Создаем текстовый узел для переноса строки
      const newLine = document.createTextNode('\n');

      // Создаем текстовый узел для контента
      const contentNode = document.createTextNode(restContent);
      const contentWrapper = document.createElement('span');
      contentWrapper.style.display = 'none';
      contentWrapper.style.paddingLeft = '15px';
      contentWrapper.appendChild(contentNode);

      // Объединяем стрелку и заголовок в один кликабельный элемент
      const clickable = document.createElement('span');
      clickable.style.cursor = 'pointer';
      clickable.appendChild(arrowSpan);
      clickable.appendChild(titleNode);

      // Вставляем элементы
      range.insertNode(contentWrapper);
      range.insertNode(newLine);
      range.insertNode(clickable);

      // Добавляем обработчик клика
      clickable.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        if (contentWrapper.style.display === 'none') {
          contentWrapper.style.display = 'block';
          arrowSpan.textContent = '▼ ';
        } else {
          contentWrapper.style.display = 'none';
          arrowSpan.textContent = '▶ ';
        }
      });

      // Сохраняем изменения
      const chatNote = document.getElementById('chatNote');
      if (chatNote) {
        chatNote.dispatchEvent(new Event('input', { bubbles: true }));
        setTimeout(() => chatNote.dispatchEvent(new Event('blur')), 100);
      }

      showPopup('Фолдаут создан');
    }
  })();

    function removeMessageClickHandlers(handler) {
      const msgs = document.querySelectorAll(".bubble .message");
      msgs.forEach(m => m.removeEventListener("click", handler));
    }

    document.getElementById("setStart").addEventListener("click", () => {
      showPopup("Нажмите на сообщение, которое будет началом логирования.");
      const msgs = document.querySelectorAll(".bubble .message");
      msgs.forEach(msg => msg.addEventListener("click", setStartHandler));
    });

    function setStartHandler(e) {
      e.stopPropagation();
      startMessageElem = e.currentTarget;
      document.querySelectorAll('.start-selected').forEach(elem => elem.classList.remove('start-selected'));
      startMessageElem.classList.add('start-selected');

      startMessageElem.querySelectorAll('.message-marker').forEach(el => el.remove());

      const startMarker = document.createElement('span');
      startMarker.className = 'message-marker';
      startMarker.style.marginLeft = '5px';
      startMarker.textContent = '⬆️';
      startMessageElem.appendChild(startMarker);

      const startBubble = startMessageElem.closest('.bubble');
      const startMid = startBubble?.getAttribute('data-mid');
      logger.info('Установлено начальное сообщение:', {
        id: startMid,
        текст: startMessageElem.innerText.slice(0, 50) + '...'
      });
      showPopup("Начало установлено.");
      localStorage.setItem("startMessageId_" + chatId, startMid);
      removeMessageClickHandlers(setStartHandler);
    }

    document.addEventListener("DOMContentLoaded", () => {
      const storedStartMid = localStorage.getItem("startMessageId_" + chatId);
      if (storedStartMid) {
        const startElem = document.querySelector(`.bubble[data-mid="${storedStartMid}"] .message`);
        if (startElem) {
          startElem.classList.add('start-selected');
          startMessageElem = startElem;
        }
      }
    });

    document.getElementById("setEnd").addEventListener("click", () => {
      showPopup("Нажмите на сообщение, которое будет концом логирования.");
      const msgs = document.querySelectorAll(".bubble .message");
      msgs.forEach(msg => msg.addEventListener("click", setEndHandler));
    });

    document.addEventListener("DOMContentLoaded", () => {
      const storedEndMid = localStorage.getItem("endMessageId_" + chatId);
      if (storedEndMid) {
        const endElem = document.querySelector(`.bubble[data-mid="${storedEndMid}"] .message`);
        if (endElem) {
          endElem.classList.add('end-selected');
          endMessageElem = endElem;
        }
      }
    });

    function setEndHandler(e) {
      e.stopPropagation();
      endMessageElem = e.currentTarget;
      document.querySelectorAll('.end-selected').forEach(elem => elem.classList.remove('end-selected'));
      endMessageElem.classList.add('end-selected');

      // Удаляем старый индикатор конца, если есть
      endMessageElem.querySelectorAll('.message-marker').forEach(el => el.remove());

      // Добавляем индикатор конца
      const endMarker = document.createElement('span');
      endMarker.className = 'message-marker';
      endMarker.style.marginLeft = '5px';
      endMarker.textContent = '🏁';
      endMessageElem.appendChild(endMarker);

      const endBubble = endMessageElem.closest('.bubble');
      const endMid = endBubble?.getAttribute('data-mid');
      logger.info('Установлено конечное сообщение:', {
        id: endMid,
        текст: endMessageElem.innerText.slice(0, 50) + '...'
      });
      showPopup("Конец установлен.");
      localStorage.setItem("endMessageId_" + chatId, endMid);
      removeMessageClickHandlers(setEndHandler);
    }

    let currentInputField = null;
    setInterval(() => {
      const newInput = document.querySelector('div.input-message-input[contenteditable="true"]');
      if (newInput !== currentInputField) {
        currentInputField = newInput;
        logger.info("Обновлено поле ввода", currentInputField);
      }
    }, 1000);

    document.getElementById("improveTextBtn").addEventListener("click", () => {
      if (currentInputField) {
        improveText(currentInputField);
      } else {
        showPopup("Поле ввода не найдено");
      }
    });

    // Функция улучшения текста
    async function improveText(inputField) {
      const originalText = inputField.innerText.trim();
      if (!originalText) return;
      const systemPrompt = localStorage.getItem("systemPrompt") || DEFAULT_SYSTEM_PROMPT;
      const selectedModel = localStorage.getItem("selectedModel") || "gpt-4.1-mini";
      const payload = {
        model: selectedModel,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `<USER MESSAGE>\n"${originalText}"\n</USER MESSAGE>` }
        ]
      };
      console.log("Запрос на улучшение текста к", selectedModel, payload);
      const API_URL = "https://api.openai.com/v1/chat/completions";
      const apiToken = "********************************************************************************************************************************************************************";
      try {
        showPopup('Генерация улучшенного текста...', true); // Make this popup persistent
        const response = await fetch(API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + apiToken
          },
          body: JSON.stringify(payload)
        });
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Ошибка API: ${response.status}. ${errorText}`);
        }
        const data = await response.json();
        logger.info("Ответ API:", data);
        if (data.choices && data.choices[0] && data.choices[0].message) {
          const improvedText = data.choices[0].message.content.trim();
          inputField.innerText = improvedText;
          hidePopup(); // Hide the persistent popup
          showPopup('Текст улучшен!'); // Show a temporary success popup
          logger.success('Текст успешно улучшен:', {
            было: originalText,
            стало: improvedText
          });
        } else {
          throw new Error('Неверный формат ответа от API');
        }
      } catch (error) {
        logger.error('Ошибка генерации улучшенного текста:', error);
        hidePopup(); // Hide the persistent popup
        showPopup('Ошибка генерации текста');
      }
    }

    document.getElementById("saveSettingsBtn").addEventListener("click", () => {
      try {
        // System prompt handling
        const systemPromptTextarea = document.getElementById("systemPromptTextarea");
        if (systemPromptTextarea) {
          const newSystemPrompt = systemPromptTextarea.value.trim();
          if (newSystemPrompt) {
            localStorage.setItem("systemPrompt", newSystemPrompt);
            logger.info("Сохранён новый systemPrompt:", newSystemPrompt);
          } else {
            showPopup("Пустой системный промпт не сохранён");
            return;
          }
        }

        // Grammar prompt handling
        const grammarPromptTextarea = document.getElementById("grammarPromptTextarea");
        if (grammarPromptTextarea) {
          const newGrammarPrompt = grammarPromptTextarea.value.trim();
          if (newGrammarPrompt) {
            localStorage.setItem("grammarPrompt", newGrammarPrompt);
            logger.info("Сохранён новый промпт для исправления грамматики:", newGrammarPrompt);
          } else {
            showPopup("Пустой промпт для исправления грамматики не сохранён");
            return;
          }
        }

        // Model display handling
        const modelDisplay = document.getElementById("modelDisplay");
        if (modelDisplay) {
          const newModel = modelDisplay.textContent.trim();
          if (newModel) {
            localStorage.setItem("selectedModel", newModel);
            logger.info("Сохранена новая глобальная модель:", newModel);
          }
        }

        // Chat model display handling
        const chatModelDisplay = document.getElementById("chatModelDisplay");
        if (chatModelDisplay) {
          const newChatModel = chatModelDisplay.textContent.trim();
          if (newChatModel) {
            localStorage.setItem("selectedChatModel", newChatModel);
            logger.info("Сохранена модель для свободного обращения:", newChatModel);
          }
        }

        // Summary model display handling
        const summaryModelDisplay = document.getElementById("summaryModelDisplay");
        if (summaryModelDisplay) {
          const newSummaryModel = summaryModelDisplay.textContent.trim();
          if (newSummaryModel) {
            localStorage.setItem("selectedSummaryModel", newSummaryModel);
            logger.info("Сохранена модель для саммари:", newSummaryModel);
          }
        }

        // Ask Chat model display handling
        const askChatModelDisplay = document.getElementById("askChatModelDisplay");
        if (askChatModelDisplay) {
          const newAskChatModel = askChatModelDisplay.textContent.trim();
          if (newAskChatModel) {
            localStorage.setItem("selectedAskChatModel", newAskChatModel);
            logger.info("Сохранена модель для Ask Chat:", newAskChatModel);
          }
        }

        // Summary prompt handling - special focus on this part
        const summaryPromptTextarea = document.getElementById("summaryPromptTextarea");
        console.log("Saving settings, summaryPromptTextarea found:", !!summaryPromptTextarea);

        if (summaryPromptTextarea) {
          const newSummaryPrompt = summaryPromptTextarea.value.trim();
          console.log("summaryPromptTextarea value:", newSummaryPrompt ? newSummaryPrompt.substring(0, 50) + "..." : "empty");

          if (newSummaryPrompt) {
            localStorage.setItem("summaryPrompt", newSummaryPrompt);
            logger.info("Сохранён новый промпт для генерации саммари:", newSummaryPrompt.substring(0, 50) + "...");
            console.log("Successfully saved summary prompt to localStorage");
          } else {
            console.warn("Empty summary prompt detected, not saving");
            showPopup("Пустой промпт для генерации саммари не сохранён");
            return;
          }
        } else {
          console.error("summaryPromptTextarea not found when trying to save settings!");
        }

        showPopup("Настройки сохранены");
      } catch(error) {
        console.error("Error saving settings:", error);
        showPopup("Ошибка при сохранении настроек");
      }
    });

    const singlePromptModelDisplay = document.getElementById("singlePromptModelDisplay");
  if (singlePromptModelDisplay) {
    const newSinglePromptModel = singlePromptModelDisplay.textContent.trim();
    if (newSinglePromptModel) {
      localStorage.setItem("selectedSinglePromptModel", newSinglePromptModel);
      logger.info("Сохранена модель для свободного GPT‑обращения:", newSinglePromptModel);
    }
  }

    const chatNoteDiv = document.getElementById("chatNote");
    chatNoteDiv.addEventListener("focus", () => {
      // Не загружаем основную заметку если активен таб
      if (currentSubNoteId) {
        return;
      }

      const savedNote = localStorage.getItem(noteCookieName) || "";
      if (
        savedNote.includes("<!--HTML_CONTENT_START-->") &&
        savedNote.includes("<!--HTML_CONTENT_END-->")
      ) {
        // Вставляем HTML напрямую
        let htmlContent = savedNote.replace(/<!--HTML_CONTENT_START-->/, "").replace(/<!--HTML_CONTENT_END-->$/, "");
        // Удаляем data-expanded, если есть
        htmlContent = htmlContent.replace(/data-expanded="[^"]+"\s*-->/, "");
        chatNoteDiv.innerHTML = htmlContent;
      } else {
        // Старое поведение: разбиваем по строкам и обрабатываем ссылки
        chatNoteDiv.innerHTML = savedNote
          .split('\n')
          .map(line => {
            return line.replace(
              /((?:https?:\/\/)?(?:www\.)?(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(?:\/[a-zA-Z0-9\-._~:\/?#[\]@!$&'()*+,;=]*)?)/g,
              (match) => {
                const fullURL = /^https?:\/\//.test(match) ? match : "https://" + match;
                return `<a href="${fullURL}" target="_blank" style="color: #4b6cb7; text-decoration: none; cursor: pointer;" title="${match}">${shortenUrl(match)}</a>`;
              }
            );
          })
          .join('<br>');
      }
    });



    chatNoteDiv.addEventListener("blur", () => {
      // Если активен таб, сохраняем в таб, а не в основную заметку
      if (currentSubNoteId) {
        saveCurrentSubNote();
        return;
      }

      // Сохраняем состояние всех фолдаутов перед обработкой
      const expandedSections = [];
      chatNoteDiv.querySelectorAll('.foldable-section.expanded').forEach((section, index) => {
          expandedSections.push(index);
      });

      // Клонируем содержимое для последующей обработки
      const tempDiv = chatNoteDiv.cloneNode(true);

      // Обработка и сохранение содержимого с сохранением HTML-структуры
      // Сохраняем информацию о развернутых секциях в отдельном теге, а не как атрибут
      const htmlContent = tempDiv.innerHTML;
      let wrappedContent;

      if (expandedSections.length > 0) {
          wrappedContent = `<!--HTML_CONTENT_START--><div data-expanded="${expandedSections.join(',')}" style="display:none;"></div>${htmlContent}<!--HTML_CONTENT_END-->`;
      } else {
          wrappedContent = `<!--HTML_CONTENT_START-->${htmlContent}<!--HTML_CONTENT_END-->`;
      }

      // Сохраняем в localStorage с сохранением HTML-структуры
      saveNote(noteCookieName, wrappedContent);
    });

  // Добавляем обработчик для предотвращения создания новых строк внутри ссылок
  chatNoteDiv.addEventListener("keydown", (e) => {
      // Проверяем, не находимся ли мы в режиме захвата горячих клавиш
      if (isShortcutCaptureMode()) {
          return;
      }

      // Сочетание клавиш Ctrl+Alt+F для Windows/Linux или Command+Option+F для Mac для создания складной секции
      if (e.key === "f" && ((e.ctrlKey && e.altKey) || (e.metaKey && e.altKey))) {
          e.preventDefault();

          const selection = window.getSelection();
          if (selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);
              const selectedText = range.toString().trim();

              if (selectedText) {
                  // Получаем первую строку как заголовок
                  const lines = selectedText.split('\n');
                  const headerText = lines[0] || 'Раздел';
                  let contentText = '';

                  // Если больше одной строки, все кроме первой - это контент
                  if (lines.length > 1) {
                      contentText = lines.slice(1).join('\n').trim();
                  }

                  // Создаем современный фолдаут на основе details/summary
                  // ВАЖНО: summary НЕ редактируется (contenteditable="false") и ВСЕГДА сворачивает/разворачивает
                  const foldableHTML = `<details class="tg-foldout"><summary contenteditable="false">${headerText}</summary><div class="tg-foldout-content" contenteditable="true">${contentText || 'Содержимое раздела'}</div></details>`;

                  // Заменяем выделенный текст на фолдаут
                  range.deleteContents();

                  // Вставляем HTML-код
                  const fragment = document.createDocumentFragment();
                  const div = document.createElement('div');
                  div.innerHTML = foldableHTML.trim();

                  while (div.firstChild) {
                      fragment.appendChild(div.firstChild);
                  }

                  range.insertNode(fragment);

                  // Создаем пустой текстовый узел после фолдаута для возможности ввода
                  const textNodeAfter = document.createTextNode('\n');
                  const foldoutElement = fragment.querySelector ? fragment.querySelector('.tg-foldout') :
                                       chatNoteDiv.querySelector('.tg-foldout:last-of-type');
                  if (foldoutElement) {
                      range.setStartAfter(foldoutElement);
                      range.insertNode(textNodeAfter);

                      // Устанавливаем курсор после фолдаута
                      range.setStartAfter(textNodeAfter);
                      range.setEndAfter(textNodeAfter);
                      const sel = window.getSelection();
                      sel.removeAllRanges();
                      sel.addRange(range);
                  }

                  // Настраиваем обработчики событий для нового фолдаута
                  const newFoldout = fragment.querySelector ? fragment.querySelector('.tg-foldout') :
                                   chatNoteDiv.querySelector('.tg-foldout:last-of-type');
                  if (newFoldout) {
                      const summaryElement = newFoldout.querySelector('summary');
                      const contentElement = newFoldout.querySelector('.tg-foldout-content');

                      // Используем глобальную систему обработчиков
                      if (summaryElement) {
                          summaryElement.setAttribute('contenteditable', 'false');
                      }

                      if (contentElement) {
                          contentElement.setAttribute('contenteditable', 'true');
                      }
                  }

                  // Показываем всплывающую подсказку
                  showPopup("Создан современный фолдаут. Содержимое можно редактировать!");
              }
          }

          // Сохраняем изменения
          chatNoteDiv.dispatchEvent(new Event('input', { bubbles: true }));
          return;
      }

      if (e.key === "Enter") {
          // Предотвращаем стандартное поведение Enter в contenteditable
          e.preventDefault();

          // Вместо сложной логики используем команду execCommand для вставки простого переноса строки
          // Это более надежный способ для contenteditable
          document.execCommand('insertLineBreak');

          // Вызываем событие input для сохранения изменений
          chatNoteDiv.dispatchEvent(new Event('input', { bubbles: true }));
          return;
      }
  });

  // Добавляем обработчик для вставки изображений из буфера обмена
  chatNoteDiv.addEventListener("paste", (e) => {
      const items = (e.clipboardData || e.originalEvent.clipboardData).items;

      for (let i = 0; i < items.length; i++) {
          if (items[i].type.indexOf("image") !== -1) {
              e.preventDefault();

              // Получаем файл изображения из буфера обмена
              const blob = items[i].getAsFile();
              const reader = new FileReader();

              reader.onload = function(event) {
                  // Создаем элемент изображения
                  const img = document.createElement("img");
                  img.src = event.target.result;

                  // Вставляем изображение в текущую позицию курсора
                  const selection = window.getSelection();
                  if (selection.rangeCount > 0) {
                      const range = selection.getRangeAt(0);
                      range.deleteContents();
                      range.insertNode(img);

                      // Перемещаем курсор после вставленного изображения
                      range.setStartAfter(img);
                      range.setEndAfter(img);
                      selection.removeAllRanges();
                      selection.addRange(range);

                      // Сохраняем изменения
                      chatNoteDiv.dispatchEvent(new Event('input', { bubbles: true }));
                  }
              };

              // Читаем изображение как Data URL
              reader.readAsDataURL(blob);
              return;
          }
      }
  });

  // Добавляем обработчик для клика по ссылкам
  chatNoteDiv.addEventListener("click", (e) => {
      const link = e.target.closest('a');
      if (link) {
          e.preventDefault();
          window.open(link.href, '_blank');
      }
  });

  // Обновляем обработчик blur для сохранения HTML-структуры с раскрывающимися разделами и картинками
  chatNoteDiv.addEventListener("blur", () => {
      // Сохраняем состояние всех фолдаутов перед обработкой
      const expandedSections = [];

      // Обрабатываем новые details-фолдауты
      chatNoteDiv.querySelectorAll('.tg-foldout').forEach((details, index) => {
          if (details.open) {
              expandedSections.push(`tg-${index}`);
          }
      });

      // Обрабатываем старые фолдауты для совместимости
      chatNoteDiv.querySelectorAll('.foldable-section.expanded').forEach((section, index) => {
          expandedSections.push(`old-${index}`);
      });

      // Клонируем содержимое для последующей обработки
      const tempDiv = chatNoteDiv.cloneNode(true);

      // Обработка и сохранение содержимого с сохранением HTML-структуры
      const htmlContent = tempDiv.innerHTML;
      let wrappedContent;

      if (expandedSections.length > 0) {
          wrappedContent = `<!--HTML_CONTENT_START--><div data-expanded="${expandedSections.join(',')}" style="display:none;"></div>${htmlContent}<!--HTML_CONTENT_END-->`;
      } else {
          wrappedContent = `<!--HTML_CONTENT_START-->${htmlContent}<!--HTML_CONTENT_END-->`;
      }

      // Сохраняем в localStorage с сохранением HTML-структуры
      saveNote(noteCookieName, wrappedContent);

      // ВАЖНО: Восстанавливаем обработчики фолдаутов после сохранения
      setTimeout(() => {
          activateFoldouts(chatNoteDiv);
          activateAllFoldouts();
      }, 50);
  });

  // Добавляем обработчик input для немедленного восстановления фолдаутов при редактировании
  chatNoteDiv.addEventListener("input", () => {
      // Восстанавливаем обработчики фолдаутов с небольшой задержкой
      setTimeout(() => {
          activateFoldouts(chatNoteDiv);
      }, 100);
  });

  // Обновляем функцию загрузки заметок при клике на вкладку
  tabNotesBtn.addEventListener("click", () => {
      activateTab(tabNotesBtn, tabNotesContent);
      const savedNote = loadNote(noteCookieName) || "";
      const chatNoteDiv = document.getElementById("chatNote");

      // Проверяем, содержит ли заметка HTML-структуру
      if (savedNote.includes('<!--HTML_CONTENT_START-->') && savedNote.includes('<!--HTML_CONTENT_END-->')) {
          try {
              // Извлекаем HTML-контент
              let htmlContent = savedNote.replace(/<!--HTML_CONTENT_START-->/, '')
                                .replace(/<!--HTML_CONTENT_END-->$/, '');

              // Проверяем, есть ли информация о развернутых секциях
              let expandedSections = [];
              const match = htmlContent.match(/data-expanded="([^"]+)"\s*-->/);

              if (match && match[1]) {
                  expandedSections = match[1].split(',').map(Number);
                  // Удаляем эту информацию из HTML
                  htmlContent = htmlContent.replace(/data-expanded="[^"]+"\s*-->/, '');
              } else {
                  htmlContent = htmlContent.replace(/-->/, '');
              }

              chatNoteDiv.innerHTML = htmlContent;

              // Восстанавливаем состояние новых details-фолдаутов
              chatNoteDiv.querySelectorAll('.tg-foldout').forEach((details, index) => {
                  if (expandedSections.includes(`tg-${index}`)) {
                      details.open = true;
                  }
              });

              // Добавляем обработчики для старых складных разделов и восстанавливаем состояние (для совместимости)
              chatNoteDiv.querySelectorAll('.foldable-section').forEach((section, index) => {
                  const header = section.querySelector('.foldable-header');
                  if (header) {
                      // Используем onclick вместо addEventListener для избежания дублирования обработчиков
                      header.onclick = function(e) {
                          e.preventDefault();
                          e.stopPropagation();
                          this.parentElement.classList.toggle('expanded');
                          showPopup("Раздел " + (this.parentElement.classList.contains('expanded') ? "развёрнут" : "свёрнут"));
                          return false;
                      };

                      // Восстанавливаем состояние раздела (свернут/развернут)
                      if (expandedSections.includes(`old-${index}`) || expandedSections.includes(index)) {
                          section.classList.add('expanded');
                      }
                  }
              });
          } catch(e) {
              console.error("Ошибка при обработке заметки:", e);
              showPopup("Ошибка при загрузке заметки: " + e.message, true);
              // Запасной вариант
              chatNoteDiv.innerHTML = "";
          }
      } else {
          // Если сохраненный контент без специальных маркеров, обрабатываем по-старому
          const formattedHTML = savedNote
              .split('\n')
              .map(line => {
                  return line.replace(
                      /((?:https?:\/\/)?(?:www\.)?(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(?:\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)?)/g,
                      (match) => {
                          const fullURL = /^https?:\/\//.test(match) ? match : "https://" + match;
                          return `<a href="${fullURL}" target="_blank" style="color: #4b6cb7; text-decoration: none; cursor: pointer;" title="${match}">${shortenUrl(match)}</a>`;
                      }
                  );
              })
              .join('<br>');

          chatNoteDiv.innerHTML = formattedHTML;
      }
  });

    // Удалено лишнее дублирование загрузки заметок

    let lastUrl = window.location.href;
  setInterval(() => {
    if (window.location.href !== lastUrl) {
      lastUrl = window.location.href;
      chatId = window.location.hash.substring(1) || "default";
      noteCookieName = "telegram_chat_note_" + chatId;
      chatNoteDiv.setAttribute("placeholder", "Заметки для чата " + chatId);

      const savedNote = loadNote(noteCookieName) || "";

      // Проверяем, содержит ли заметка HTML-структуру
      if (savedNote.includes('<!--HTML_CONTENT_START-->') && savedNote.includes('<!--HTML_CONTENT_END-->')) {
          try {
              // Извлекаем HTML-контент
              let htmlContent = savedNote.replace(/<!--HTML_CONTENT_START-->/, '')
                                .replace(/<!--HTML_CONTENT_END-->$/, '');

              // Проверяем, есть ли информация о развернутых секциях
              let expandedSections = [];
              const match = htmlContent.match(/data-expanded="([^"]+)"\s*-->/);

              if (match && match[1]) {
                  expandedSections = match[1].split(',').map(Number);
                  // Удаляем эту информацию из HTML
                  htmlContent = htmlContent.replace(/data-expanded="[^"]+"\s*-->/, '');
              } else {
                  htmlContent = htmlContent.replace(/-->/, '');
              }

              chatNoteDiv.innerHTML = htmlContent;

              // Добавляем обработчики для складных разделов и восстанавливаем состояние
              chatNoteDiv.querySelectorAll('.foldable-section').forEach((section, index) => {
                  const header = section.querySelector('.foldable-header');
                  if (header) {
                      // Используем onclick вместо addEventListener для избежания дублирования обработчиков
                      header.onclick = function(e) {
                          e.preventDefault();
                          e.stopPropagation();
                          this.parentElement.classList.toggle('expanded');
                          showPopup("Раздел " + (this.parentElement.classList.contains('expanded') ? "развёрнут" : "свёрнут"));
                          return false;
                      };

                      // Восстанавливаем состояние раздела (свернут/развернут)
                      if (expandedSections.includes(index)) {
                          section.classList.add('expanded');
                      }
                  }
              });
          } catch(e) {
              console.error("Ошибка при обработке заметки:", e);
              showPopup("Ошибка при загрузке заметки: " + e.message, true);
              // Запасной вариант
              chatNoteDiv.innerHTML = "";
          }
      } else {
          // Если сохраненный контент без специальных маркеров, обрабатываем по-старому
          const formattedHTML = savedNote
              .split('\n')
              .map(line => {
                  return line.replace(
                      /((?:https?:\/\/)?(?:www\.)?(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(?:\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)?)/g,
                      (match) => {
                          const fullURL = /^https?:\/\//.test(match) ? match : "https://" + match;
                          return `<a href="${fullURL}" target="_blank" style="color: #4b6cb7; text-decoration: none; cursor: pointer;" title="${match}">${shortenUrl(match)}</a>`;
                      }
                  );
              })
              .join('<br>');

          chatNoteDiv.innerHTML = formattedHTML;
      }

      showPopup("Заметки обновлены для чата " + chatId);
      logger.info('Изменение URL: Обновлены заметки для чата', chatId);

      // Загружаем подзаметки для нового чата
      loadSubNotes();

      if (document.getElementById('tab-quick-content').classList.contains('active')) {
        updateGlobalQuickMessages();
        updateChatQuickMessages();
      }

      updateMultiSelectOverlays();
    }
  }, 1000);

    window.addEventListener("hashchange", () => {
      chatId = window.location.hash.substring(1) || "default";
      noteCookieName = "telegram_chat_note_" + chatId;

      // Обновляем заметки
      chatNoteDiv.setAttribute("placeholder", "Заметки для чата " + chatId);
      const savedNote = loadNote(noteCookieName) || "";

      // Загружаем подзаметки для нового чата
      loadSubNotes();

      // Проверяем, содержит ли заметка HTML-структуру
      if (savedNote.includes('<!--HTML_CONTENT_START-->') && savedNote.includes('<!--HTML_CONTENT_END-->')) {
          try {
              // Извлекаем HTML-контент
              let htmlContent = savedNote.replace(/<!--HTML_CONTENT_START-->/, '')
                            .replace(/<!--HTML_CONTENT_END-->$/, '');

              // Проверяем, есть ли информация о развернутых секциях
              let expandedSections = [];
              const match = htmlContent.match(/data-expanded="([^"]+)"\s*-->/);

              if (match && match[1]) {
                  expandedSections = match[1].split(',').map(Number);
                  // Удаляем эту информацию из HTML
                  htmlContent = htmlContent.replace(/data-expanded="[^"]+"\s*-->/, '');
              } else {
                  htmlContent = htmlContent.replace(/-->/, '');
              }

              chatNoteDiv.innerHTML = htmlContent;

              // Добавляем обработчики для складных разделов и восстанавливаем состояние
              chatNoteDiv.querySelectorAll('.foldable-section').forEach((section, index) => {
                  const header = section.querySelector('.foldable-header');
                  if (header) {
                      // Используем onclick вместо addEventListener для избежания дублирования обработчиков
                      header.onclick = function(e) {
                          e.preventDefault();
                          e.stopPropagation();
                          this.parentElement.classList.toggle('expanded');
                          showPopup("Раздел " + (this.parentElement.classList.contains('expanded') ? "развёрнут" : "свёрнут"));
                          return false;
                      };

                      // Восстанавливаем состояние раздела (свернут/развернут)
                      if (expandedSections.includes(index)) {
                          section.classList.add('expanded');
                      }
                  }
              });
          } catch(e) {
              console.error("Ошибка при обработке заметки:", e);
              showPopup("Ошибка при загрузке заметки: " + e.message, true);
              // Запасной вариант
              chatNoteDiv.innerHTML = "";
          }
      } else {
          // Если сохраненный контент без специальных маркеров, обрабатываем по-старому
          const formattedHTML = savedNote
              .split('\n')
              .map(line => {
                  return line.replace(
                      /((?:https?:\/\/)?(?:www\.)?(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(?:\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)?)/g,
                      (match) => {
                          const fullURL = /^https?:\/\//.test(match) ? match : "https://" + match;
                          return `<a href="${fullURL}" target="_blank" style="color: #4b6cb7; text-decoration: none; cursor: pointer;" title="${match}">${shortenUrl(match)}</a>`;
                      }
                  );
              })
              .join('<br>');

          chatNoteDiv.innerHTML = formattedHTML;
      }

      showPopup("Заметки обновлены для чата " + chatId);
      logger.info('Изменение URL: Обновлены заметки для чата', chatId);

      if (document.getElementById('tab-quick-content').classList.contains('active')) {
        updateGlobalQuickMessages();
        updateChatQuickMessages();
      }

      updateMultiSelectOverlays();
    });

    const systemPromptTextarea = document.getElementById("systemPromptTextarea");
    systemPromptTextarea.addEventListener("keydown", e => e.stopPropagation());

    // Глобальный обработчик шортката для исправления грамматики с использованием флага
    document.addEventListener("keydown", (e) => {
      const activeEl = document.activeElement;
      if (activeEl && (activeEl.id === "grammarPromptTextarea" || activeEl.id === "systemPromptTextarea" || activeEl.id === "summaryPromptTextarea")) {
        return;
      }
      const shortcutCorrectGrammar = localStorage.getItem("shortcut-correctGrammar") || "Command+G";
      if (checkShortcutMatch(e, shortcutCorrectGrammar)) {
        e.preventDefault();
        if (!isGrammarProcessing) {
          isGrammarProcessing = true;
          showPopup("Запуск исправления грамматики");
          if (currentInputField) {
            correctGrammar(currentInputField);
          } else {
            showPopup("Поле ввода не найдено");
          }
          setTimeout(() => { isGrammarProcessing = false; }, 1000);
        }
      }
    });

    let popupTimeout;
    function showPopup(msg, persistent = false, duration = 2000) {
      let popup = document.getElementById("custom-popup");
      if (!popup) {
        popup = document.createElement("div");
        popup.id = "custom-popup";
        popup.style.position = "fixed";
        popup.style.top = "20px";
        popup.style.left = "50%";
        popup.style.transform = "translateX(-50%)";
        popup.style.backgroundColor = "rgba(0,0,0,0.8)";
        popup.style.color = "#fff";
        popup.style.padding = "10px 20px";
        popup.style.borderRadius = "5px";
        popup.style.zIndex = "11000";
        popup.style.transition = "opacity 0.3s ease";
        document.body.appendChild(popup);
      }
      popup.textContent = msg;
      popup.style.opacity = "1";

      if (popupTimeout) clearTimeout(popupTimeout);

      // Only set a timeout if the popup is not meant to be persistent
      if (!persistent) {
        popupTimeout = setTimeout(() => { popup.style.opacity = "0"; }, duration);
      }
    }

    function hidePopup() {
      const popup = document.getElementById("custom-popup");
      if (popup) {
        popup.style.opacity = "0";
      }

      // Only hide the backdrop if the AI prompt is not visible
      const singlePromptVisible = singlePromptContainer &&
                                singlePromptContainer.style.display !== 'none';

      if (!singlePromptVisible) {
        const backdrop = document.getElementById('ai-backdrop');
        if (backdrop) backdrop.style.display = 'none';
      }
    }

    // Функция обновления счетчика сообщений
    function updateMessageCounter() {
      const counter = document.getElementById('messageCounter');
      const inlineCounter = document.getElementById('message-count-inline');

      // if (!counter) return; // Original counter, might be removed or not used
      const messagesMap = chatMessages[chatId];
      const count = messagesMap ? messagesMap.size : 0;

      if (counter) { // Safely update if old counter exists
        counter.textContent = `Сообщений в памяти: ${count}`;
      }

      // Обновляем встроенный счетчик в кнопке Ask AI
      if (inlineCounter) {
        inlineCounter.textContent = count;
      }
    }

    // Функция сохранения файла
    function downloadTxtFile(text, filename) {
      logger.info('Сохранение файла:', {
        имяФайла: filename,
        размерТекста: text.length,
        первыеСтроки: text.split('\n').slice(0, 3).join('\n')
      });
      const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }

    // Обработчик для копирования лога с использованием никнейма из span.peer-title и двойными переводами строк
    document.getElementById("copyLog").addEventListener("click", async () => {
      logger.info('Начало операции копирования лога');
      const messagesForChat = chatMessages[chatId] ? Array.from(chatMessages[chatId].values()) : [];
      const finalMessages = messagesForChat.sort((a, b) => a.mid - b.mid);

      let logText = "";
      if (!startMessageElem) {
        logger.info('Начало не установлено, копируем все сообщения:', finalMessages.length);
        showPopup(`Копирование всех сообщений (${finalMessages.length})...`);
        finalMessages.forEach(msg => {
          const sender = getSenderName(msg);
          const messageText = processMessageText(msg);
          logText += `${sender}: ${messageText}\n\n`;
        });
        try {
          await navigator.clipboard.writeText(logText);
          showPopup("Лог переписки скопирован!");
        } catch {
          showPopup("Ошибка копирования");
        }
        return;
      }

      const startBubble = startMessageElem.closest('.bubble');
      const startMid = startBubble?.getAttribute('data-mid');
      if (!startMid) { showPopup("Начальное сообщение не найдено!"); return; }
      const startIndex = finalMessages.findIndex(msg => msg.mid === parseInt(startMid, 10));
      let endIndex;
      if (endMessageElem) {
        const endBubble = endMessageElem.closest('.bubble');
        const endMid = endBubble?.getAttribute('data-mid');
        const foundEnd = finalMessages.findIndex(msg => msg.mid === parseInt(endMid, 10));
        endIndex = foundEnd !== -1 ? foundEnd : finalMessages.length - 1;
      } else {
        endIndex = finalMessages.findIndex(msg => msg.isLast) || finalMessages.length - 1;
      }
      const [from, to] = [Math.min(startIndex, endIndex), Math.max(startIndex, endIndex)];
      logger.info('Выбранный диапазон сообщений:', { с: from, по: to, всегоСообщений: to - from + 1 });
      for (let i = from; i <= to; i++) {
        const msg = finalMessages[i];
        const sender = getSenderName(msg);
        const messageText = processMessageText(msg);
        logText += `${sender}: ${messageText}\n\n`;
      }
      try {
        await navigator.clipboard.writeText(logText);
        showPopup("Лог переписки скопирован!");
      } catch {
        showPopup("Ошибка копирования");
      }
    });

    // Обработчик для сохранения лога в файл с использованием никнейма и двойными переводами строк
    document.getElementById("saveLog").addEventListener("click", async () => {
      const finalMessages = Array.from(chatMessages[chatId].values()).sort((a, b) => a.mid - b.mid);
      let logText = "";
      const currentDate = new Date().toLocaleString();
      logText += `Лог переписки от ${currentDate}\n\n`;

      if (!startMessageElem) {
        logger.info('Начало не установлено, сохраняем все сообщения:', finalMessages.length);
        finalMessages.forEach(msg => {
          const sender = getSenderName(msg);
          const messageText = processMessageText(msg);
          logText += `${sender}: ${messageText}\n\n`;
        });
      } else {
        const startBubble = startMessageElem.closest('.bubble');
        const startMid = startBubble?.getAttribute('data-mid');
        if (!startMid) { showPopup("Начальное сообщение не найдено!"); return; }
        const startIndex = finalMessages.findIndex(msg => msg.mid === parseInt(startMid, 10));
        let endIndex;
        if (endMessageElem) {
          const endBubble = endMessageElem.closest('.bubble');
          const endMid = endBubble?.getAttribute('data-mid');
          const foundEnd = finalMessages.findIndex(msg => msg.mid === parseInt(endMid, 10));
          endIndex = foundEnd !== -1 ? foundEnd : finalMessages.length - 1;
        } else {
          endIndex = finalMessages.findIndex(msg => msg.isLast) || finalMessages.length - 1;
        }
        const [from, to] = [Math.min(startIndex, endIndex), Math.max(startIndex, endIndex)];
        for (let i = from; i <= to; i++) {
          const msg = finalMessages[i];
          const sender = getSenderName(msg);
          const messageText = processMessageText(msg);
          logText += `${sender}: ${messageText}\n\n`;
        }
      }
      try {
        downloadTxtFile(logText, 'telegram-log.txt');
        showPopup("Лог сохранен в файл!");
      } catch {
        showPopup("Ошибка сохранения файла");
      }
    });

    document.addEventListener("keydown", (e) => {
      // Skip shortcut handling if we're in a textarea or input
      const activeElement = document.activeElement;
      if (activeElement &&
          (activeElement.tagName === 'TEXTAREA' ||
          activeElement.tagName === 'INPUT' ||
          activeElement.id === 'summaryPromptTextarea' ||
          activeElement.id === 'systemPromptTextarea' ||
          activeElement.id === 'grammarPromptTextarea' ||
          activeElement.getAttribute('contenteditable') === 'true')) {
        console.log("Skipping keydown handler due to active element:", activeElement.tagName, activeElement.id);
        return;
      }

      if (e.metaKey && e.key.toLowerCase() === "j") {
        e.preventDefault();
        if (!isImprovementProcessing) {
          isImprovementProcessing = true;
          showPopup("Запуск улучшения текста");
        const improveBtn = document.getElementById("improveTextBtn");
        if (improveBtn) {
          improveBtn.click();
          }
          setTimeout(() => { isImprovementProcessing = false; }, 1000);
        }
      }

      const shortcutSwitchLeft = localStorage.getItem("shortcut-switchLeft") || "Control+ArrowLeft";
      if (checkShortcutMatch(e, shortcutSwitchLeft)) {
        e.preventDefault();
        navigateFolder(-1);
      }

      const shortcutSwitchRight = localStorage.getItem("shortcut-switchRight") || "Control+ArrowRight";
      if (checkShortcutMatch(e, shortcutSwitchRight)) {
        e.preventDefault();
        navigateFolder(1);
      }

      const shortcutSearch = localStorage.getItem("shortcut-search") || "Command+K";
      if (checkShortcutMatch(e, shortcutSearch)) {
        e.preventDefault();
        const searchContainer = document.querySelector('.input-search');
        if (searchContainer) {
          const clickEvent = new MouseEvent("click", {
            view: window,
            bubbles: true,
            cancelable: true
          });
          searchContainer.dispatchEvent(clickEvent);
          const searchInput = searchContainer.querySelector('.input-search-input');
          if (searchInput) {
            searchInput.focus();
          }
        }
      }
    });

    // Функция для захвата нового шортката
    function captureShortcut(inputElement, storageKey) {
      inputElement.addEventListener("focus", () => {
        inputElement.value = "";
        const handler = function(e) {
          e.preventDefault();
          e.stopPropagation();
          const isModifierOnly = ["Control", "Shift", "Alt", "Meta"].includes(e.key);
          if (isModifierOnly) {
            return;
          }
          if (!(e.ctrlKey || e.metaKey || e.altKey || e.shiftKey)) {
            return;
          }
          let parts = [];
          if (e.ctrlKey) parts.push("Control");
          if (e.metaKey) parts.push("Command");
          if (e.altKey) parts.push("Alt");
          if (e.shiftKey) parts.push("Shift");
          parts.push(e.key);
          const shortcut = parts.join("+");
          inputElement.value = shortcut;
          localStorage.setItem(storageKey, shortcut);
          document.removeEventListener("keydown", handler, true);
          inputElement.blur();
        };
        document.addEventListener("keydown", handler, true);
      });
    }

    function checkShortcutMatch(e, shortcutString) {
      // Skip shortcut handling if we're in a textarea or input
      const activeElement = document.activeElement;
      if (activeElement &&
          (activeElement.tagName === 'TEXTAREA' ||
          activeElement.tagName === 'INPUT' ||
          activeElement.id === 'summaryPromptTextarea' ||
          activeElement.id === 'systemPromptTextarea' ||
          activeElement.id === 'grammarPromptTextarea' ||
          activeElement.getAttribute('contenteditable') === 'true')) {
        // Only log if this is a potential shortcut match to avoid console spam
        if (e.ctrlKey || e.metaKey || e.altKey || e.shiftKey) {
          console.log("Ignoring potential shortcut in input element:", activeElement.tagName, activeElement.id);
        }
        return false;
      }

      const parts = shortcutString.split("+").map(p => p.trim());
      let requiredModifiers = { Control: false, Shift: false, Alt: false, Command: false };
      let requiredKey = "";
      parts.forEach(part => {
        if (["Control", "Shift", "Alt", "Command"].includes(part)) {
          requiredModifiers[part] = true;
        } else {
          requiredKey = part.toLowerCase();
        }
      });
      if (requiredModifiers.Control !== e.ctrlKey) return false;
      if (requiredModifiers.Shift !== e.shiftKey) return false;
      if (requiredModifiers.Alt !== e.altKey) return false;
      if (requiredModifiers.Command !== e.metaKey) return false;
      if (requiredKey && e.key.toLowerCase() !== requiredKey) return false;
      return true;
    }

    // Функция навигации между папками
    function navigateFolder(direction) {
      const folders = Array.from(document.querySelectorAll('.menu-horizontal-div-item.rp'));
      if (!folders.length) {
        console.warn("Папки не найдены");
        return;
      }
      const activeIndex = folders.findIndex(folder => folder.classList.contains('active'));
      if (activeIndex === -1) {
        console.warn("Активная папка не найдена");
        return;
      }
      let newIndex = activeIndex + direction;
      if (newIndex >= folders.length) newIndex = 0;
      if (newIndex < 0) newIndex = folders.length - 1;
      const newFolder = folders[newIndex];
      if (newFolder) {
        const clickEvent = new MouseEvent("click", {
          view: window,
          bubbles: true,
          cancelable: true
        });
        newFolder.dispatchEvent(clickEvent);
        logger.info("Переключение папки: новый индекс", newIndex);
      } else {
        console.warn("Новая папка не найдена");
      }
    }


    document.addEventListener("keydown", (e) => {
      if (checkShortcutMatch(e, "Command+C") && multiSelectedMessages.length > 0) {
        e.preventDefault();
        const selectedMessages = multiSelectedMessages.filter(item => item.chatId === chatId);
        const textToCopy = selectedMessages.map(item => {
          return item.text.replace(/\b\d{1,2}:\d{2}(?::\d{2})?\b/g, '').trim();
        }).join("\n\n");

        navigator.clipboard.writeText(textToCopy)
          .then(() => {
            showPopup("Выделенные сообщения скопированы в буфер обмена!");
          });
      }
    });

    // Тултип для кнопки "Улучшить текст"
    function initTooltip() {
      const improveTextBtn = document.getElementById("improveTextBtn");
      if (improveTextBtn) {
        const tooltip = document.createElement("div");
        tooltip.style.position = "fixed";
        tooltip.style.backgroundColor = "rgba(0, 0, 0, 0.8)";
        tooltip.style.color = "#fff";
        tooltip.style.padding = "5px 10px";
        tooltip.style.borderRadius = "3px";
        tooltip.style.fontSize = "12px";
        tooltip.style.pointerEvents = "none";
        tooltip.style.opacity = "0";
        tooltip.style.transition = "opacity 0.2s ease";
        tooltip.style.zIndex = "11001";
        tooltip.innerText = "Command+J: Улучшить текст";
        document.body.appendChild(tooltip);

        improveTextBtn.addEventListener("mouseenter", () => {
          const rect = improveTextBtn.getBoundingClientRect();
          const tooltipHeight = tooltip.offsetHeight;
          tooltip.style.left = rect.left + "px";
          tooltip.style.top = (rect.top - tooltipHeight - 5) + "px";
          tooltip.style.opacity = "1";
        });
        improveTextBtn.addEventListener("mouseleave", () => {
          tooltip.style.opacity = "0";
        });
      }
    }
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", initTooltip);
    } else {
      initTooltip();
    }

    // Функция исправления грамматики с использованием API токена для gpt-4o-mini
  const DEFAULT_GRAMMAR_PROMPT = `<system_prompt>
  ТЫ — ЛУЧШИЙ В МИРЕ ЭКСПЕРТ ПО ГРАММАТИКЕ И ЛЕКСИКЕ, ЛИНГВИСТ С ВЫСШЕЙ КВАЛИФИКАЦИЕЙ. ТВОЯ ЗАДАЧА — ИСПРАВИТЬ ВСЕ ГРАММАТИЧЕСКИЕ, ПУНКТУАЦИОННЫЕ И ЛЕКСИЧЕСКИЕ ОШИБКИ В ПРЕДОСТАВЛЕННОМ ТЕКСТЕ, СОХРАНИВ ПРИ ЭТОМ ИСХОДНЫЙ СМЫСЛ И СТРУКТУРУ ПРЕДЛОЖЕНИЙ.

  ###ИНСТРУКЦИИ###
  - ИСПРАВЛЯЙ ТОЛЬКО ОШИБКИ В ГРАММАТИКЕ, ПУНКТУАЦИИ И ЛЕКСИКЕ
  - СОХРАНЯЙ ИСХОДНЫЙ СМЫСЛ, ЛОГИКУ И СТРУКТУРУ ПРЕДЛОЖЕНИЙ
  - НЕ ДОБАВЛЯЙ НОВУЮ ИНФОРМАЦИЮ И НЕ ИЗМЕНЯЙ СОДЕРЖАНИЕ
  - СЛЕДИ ЗА СОГЛАСОВАНИЕМ ВРЕМЕН, ПРАВИЛЬНЫМ УПОТРЕБЛЕНИЕМ СЛОВ И ПРАВИЛЬНОЙ ПОСТАНОВКОЙ ЗАПЯТЫХ
  ###ЦЕПОЧКА РАССУЖДЕНИЙ###
  1. АНАЛИЗ:
    - ВНИМАТЕЛЬНО ПРОЧИТАЙ ИСХОДНЫЙ ТЕКСТ
    - ОПРЕДЕЛИ ГРАММАТИЧЕСКИЕ И ЛЕКСИЧЕСКИЕ ОШИБКИ
  2. ИСПРАВЛЕНИЕ:
    - ИСПРАВЬ НАЙДЕННЫЕ ОШИБКИ, СОХРАНИВ СТРУКТУРУ И СМЫСЛ
    - УСТРАНИ НЕКОРРЕКТНЫЕ ЛЕКСИЧЕСКИЕ СОЧЕТАНИЯ, ПОДОБРАВ БОЛЕЕ УМЕСТНЫЕ СЛОВА
  3. ПРОВЕРКА:
    - УБЕДИСЬ, ЧТО ИСПРАВЛЕНИЯ НЕ ИЗМЕНИЛИ ИСХОДНЫЙ СМЫСЛ
    - ПРОВЕРЬ, ЧТО ВСЕ ПРАВИЛА ГРАММАТИКИ И ПУНКТУАЦИИ СОБЛЮДЕНЫ

  ###ЧЕГО НЕ ДЕЛАТЬ###
  - НИКОГДА НЕ ИЗМЕНЯЙ СМЫСЛ ПРЕДЛОЖЕНИЯ
  - НЕ ПЕРЕФРАЗИРУЙ И НЕ ПЕРЕСТРАИВАЙ ПРЕДЛОЖЕНИЯ БЕЗ НЕОБХОДИМОСТИ
  - НЕ ДОБАВЛЯЙ НОВЫЕ ДЕТАЛИ ИЛИ КОММЕНТАРИИ
  - НЕ УПРОЩАЙ И НЕ УСЛОЖНЯЙ ТЕКСТ, ЕСЛИ ЭТО НЕ ОТНОСИТСЯ К ИСПРАВЛЕНИЮ ОШИБОК
  </system_prompt>`;

  async function correctGrammar(inputField) {
    const originalText = inputField.innerText.trim();
    if (!originalText) return;
    const grammarPrompt = localStorage.getItem("grammarPrompt") || DEFAULT_GRAMMAR_PROMPT;
    const payload = {
      model: "o3-mini",
      messages: [
        { role: 'system', content: grammarPrompt },
        { role: 'user', content: `<USER MESSAGE>\n"${originalText}"\n</USER MESSAGE>` }
      ]
    };
    console.log("Запрос на исправление грамматики к 4o-mini:", payload);
    const API_URL = "https://api.openai.com/v1/chat/completions";
    const apiToken = "********************************************************************************************************************************************************************";
    try {
      showPopup('Генерация текста с исправленной грамматикой...', true); // Make this popup persistent
      const response = await fetch(API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + apiToken
        },
        body: JSON.stringify(payload)
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Ошибка API: ${response.status}. ${errorText}`);
      }
      const data = await response.json();
      logger.info("Ответ API для исправления грамматики:", data);
      if (data.choices && data.choices[0] && data.choices[0].message) {
        const correctedText = data.choices[0].message.content.trim();
        inputField.innerText = correctedText;
        hidePopup(); // Hide the persistent popup
        showPopup('Грамматика исправлена!'); // Show a temporary success popup
        logger.success('Текст успешно исправлен (грамматика):', {
          было: originalText,
          стало: correctedText
        });
      } else {
        throw new Error('Неверный формат ответа от API');
      }
    } catch (error) {
      logger.error('Ошибка исправления грамматики:', error);
      hidePopup(); // Hide the persistent popup
      showPopup('Ошибка исправления грамматики');
    }
  }

  const grammarPromptTextarea = document.getElementById("grammarPromptTextarea");
  if (grammarPromptTextarea) {
    grammarPromptTextarea.addEventListener("keydown", (e) => e.stopPropagation());
  }

    // Глобальный обработчик шортката для исправления грамматики (Command+G)
  document.addEventListener("keydown", (e) => {
    const activeEl = document.activeElement;
      if (activeEl && (activeEl.id === "grammarPromptTextarea" || activeEl.id === "systemPromptTextarea")) {
      return;
    }
    const shortcutCorrectGrammar = localStorage.getItem("shortcut-correctGrammar") || "Command+G";
    if (checkShortcutMatch(e, shortcutCorrectGrammar)) {
      e.preventDefault();
      if (!isGrammarProcessing) {
        isGrammarProcessing = true;
        showPopup("Запуск исправления грамматики");
        if (currentInputField) {
          correctGrammar(currentInputField);
        } else {
          showPopup("Поле ввода не найдено");
        }
        setTimeout(() => { isGrammarProcessing = false; }, 1000);
      }
    }
  });

    // Функция показа уведомлений (с использованием popupTimeout)
  function showPopup(msg, persistent = false) {
    let popup = document.getElementById("custom-popup");
    if (!popup) {
      popup = document.createElement("div");
      popup.id = "custom-popup";
      popup.style.position = "fixed";
      popup.style.top = "20px";
      popup.style.left = "50%";
      popup.style.transform = "translateX(-50%)";
      popup.style.backgroundColor = "rgba(0,0,0,0.8)";
      popup.style.color = "#fff";
      popup.style.padding = "10px 20px";
      popup.style.borderRadius = "5px";
      popup.style.zIndex = "11000";
      popup.style.transition = "opacity 0.3s ease";
      document.body.appendChild(popup);
    }
    popup.textContent = msg;
    popup.style.opacity = "1";

    if (popupTimeout) clearTimeout(popupTimeout);

    // Only set a timeout if the popup is not meant to be persistent
    if (!persistent) {
      popupTimeout = setTimeout(() => { popup.style.opacity = "0"; }, 2000);
    }
  }

  function hidePopup() {
    const popup = document.getElementById("custom-popup");
    if (popup) {
      popup.style.opacity = "0";
    }

    // Only hide the backdrop if the AI prompt is not visible
    const singlePromptVisible = singlePromptContainer &&
                              singlePromptContainer.style.display !== 'none';

    if (!singlePromptVisible) {
      const backdrop = document.getElementById('ai-backdrop');
      if (backdrop) backdrop.style.display = 'none';
    }
  }

    // Функция обновления счетчика сообщений
    function updateMessageCounter() {
      const counter = document.getElementById('messageCounter');
      const inlineCounter = document.getElementById('message-count-inline');

      // if (!counter) return; // Original counter, might be removed or not used
      const messagesMap = chatMessages[chatId];
      const count = messagesMap ? messagesMap.size : 0;

      if (counter) { // Safely update if old counter exists
        counter.textContent = `Сообщений в памяти: ${count}`;
      }

      // Обновляем встроенный счетчик в кнопке Ask AI
      if (inlineCounter) {
        inlineCounter.textContent = count;
      }
    }

    // Функция сохранения файла
    function downloadTxtFile(text, filename) {
      logger.info('Сохранение файла:', {
        имяФайла: filename,
        размерТекста: text.length,
        первыеСтроки: text.split('\n').slice(0, 3).join('\n')
      });
      const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }

    // Обработчик для копирования лога с использованием getSenderName и двойных переводов строк
    document.getElementById("copyLog").addEventListener("click", async () => {
      logger.info('Начало операции копирования лога');
      const messagesForChat = chatMessages[chatId] ? Array.from(chatMessages[chatId].values()) : [];
      const finalMessages = messagesForChat.sort((a, b) => a.mid - b.mid);
      let logText = "";
      if (!startMessageElem) {
        logger.info('Начало не установлено, копируем все сообщения:', finalMessages.length);
        showPopup(`Копирование всех сообщений (${finalMessages.length})...`);
        finalMessages.forEach(msg => {
          const sender = getSenderName(msg);
          const messageText = processMessageText(msg);
          logText += `${sender}: ${messageText}\n\n`;
        });
        try {
          await navigator.clipboard.writeText(logText);
          showPopup("Лог переписки скопирован!");
        } catch { showPopup("Ошибка копирования"); }
        return;
      }

      const startBubble = startMessageElem.closest('.bubble');
      const startMid = startBubble?.getAttribute('data-mid');
      if (!startMid) { showPopup("Начальное сообщение не найдено!"); return; }
      const startIndex = finalMessages.findIndex(msg => msg.mid === parseInt(startMid, 10));
      let endIndex;
      if (endMessageElem) {
        const endBubble = endMessageElem.closest('.bubble');
        const endMid = endBubble?.getAttribute('data-mid');
        const foundEnd = finalMessages.findIndex(msg => msg.mid === parseInt(endMid, 10));
        endIndex = foundEnd !== -1 ? foundEnd : finalMessages.length - 1;
      } else {
        endIndex = finalMessages.findIndex(msg => msg.isLast) || finalMessages.length - 1;
      }
      const [from, to] = [Math.min(startIndex, endIndex), Math.max(startIndex, endIndex)];
      logger.info('Выбранный диапазон сообщений:', { с: from, по: to, всегоСообщений: to - from + 1 });
      for (let i = from; i <= to; i++) {
        const msg = finalMessages[i];
        const sender = getSenderName(msg);
        const messageText = processMessageText(msg);
        logText += `${sender}: ${messageText}\n\n`;
      }
      try {
        await navigator.clipboard.writeText(logText);
        showPopup("Лог переписки скопирован!");
      }
      catch { showPopup("Ошибка копирования"); }
    });

    // Обработчик для сохранения лога в файл с использованием getSenderName и двойных переводов строк
    document.getElementById("saveLog").addEventListener("click", async () => {
      const finalMessages = Array.from(chatMessages[chatId].values()).sort((a, b) => a.mid - b.mid);
        let logText = "";
        const currentDate = new Date().toLocaleString();
        logText += `Лог переписки от ${currentDate}\n\n`;
      if (!startMessageElem) {
        logger.info('Начало не установлено, сохраняем все сообщения:', finalMessages.length);
        finalMessages.forEach(msg => {
          const sender = getSenderName(msg);
          const messageText = processMessageText(msg);
          logText += `${sender}: ${messageText}\n\n`;
        });
      } else {
      const startBubble = startMessageElem.closest('.bubble');
      const startMid = startBubble?.getAttribute('data-mid');
      if (!startMid) { showPopup("Начальное сообщение не найдено!"); return; }
      const startIndex = finalMessages.findIndex(msg => msg.mid === parseInt(startMid, 10));
      let endIndex;
      if (endMessageElem) {
        const endBubble = endMessageElem.closest('.bubble');
        const endMid = endBubble?.getAttribute('data-mid');
        const foundEnd = finalMessages.findIndex(msg => msg.mid === parseInt(endMid, 10));
        endIndex = foundEnd !== -1 ? foundEnd : finalMessages.length - 1;
        } else {
          endIndex = finalMessages.findIndex(msg => msg.isLast) || finalMessages.length - 1;
        }
      const [from, to] = [Math.min(startIndex, endIndex), Math.max(startIndex, endIndex)];
      for (let i = from; i <= to; i++) {
        const msg = finalMessages[i];
          const sender = getSenderName(msg);
          const messageText = processMessageText(msg);
          logText += `${sender}: ${messageText}\n\n`;
        }
      }
      try {
        downloadTxtFile(logText, 'telegram-log.txt');
        showPopup("Лог сохранен в файл!");
      }
      catch { showPopup("Ошибка сохранения файла"); }
    });

    document.addEventListener("keydown", (e) => {
      if (e.metaKey && e.key.toLowerCase() === "j") {
        e.preventDefault();
        if (!isImprovementProcessing) {
          isImprovementProcessing = true;
          showPopup("Запуск улучшения текста");
        const improveBtn = document.getElementById("improveTextBtn");
        if (improveBtn) {
          improveBtn.click();
        }
          setTimeout(() => { isImprovementProcessing = false; }, 1000);
        }
      }

      const shortcutSwitchLeft = localStorage.getItem("shortcut-switchLeft") || "Control+ArrowLeft";
      if (checkShortcutMatch(e, shortcutSwitchLeft)) {
        e.preventDefault();
        navigateFolder(-1);
      }

      const shortcutSwitchRight = localStorage.getItem("shortcut-switchRight") || "Control+ArrowRight";
      if (checkShortcutMatch(e, shortcutSwitchRight)) {
        e.preventDefault();
        navigateFolder(1);
      }

      const shortcutSearch = localStorage.getItem("shortcut-search") || "Command+K";
      if (checkShortcutMatch(e, shortcutSearch)) {
        e.preventDefault();
        const searchContainer = document.querySelector('.input-search');
        if (searchContainer) {
          const clickEvent = new MouseEvent("click", {
            view: window,
            bubbles: true,
            cancelable: true
          });
          searchContainer.dispatchEvent(clickEvent);
          const searchInput = searchContainer.querySelector('.input-search-input');
          if (searchInput) {
            searchInput.focus();
          }
        }
      }
    });

    // Функция для захвата нового шортката
    function captureShortcut(inputElement, storageKey) {
      inputElement.addEventListener("focus", () => {
        inputElement.value = "";
        const handler = function(e) {
          e.preventDefault();
          e.stopPropagation();
          const isModifierOnly = ["Control", "Shift", "Alt", "Meta"].includes(e.key);
          if (isModifierOnly) {
            return;
          }
          if (!(e.ctrlKey || e.metaKey || e.altKey || e.shiftKey)) {
            return;
          }
          let parts = [];
          if (e.ctrlKey) parts.push("Control");
          if (e.metaKey) parts.push("Command");
          if (e.altKey) parts.push("Alt");
          if (e.shiftKey) parts.push("Shift");
          parts.push(e.key);
          const shortcut = parts.join("+");
          inputElement.value = shortcut;
          localStorage.setItem(storageKey, shortcut);
          document.removeEventListener("keydown", handler, true);
          inputElement.blur();
        };
        document.addEventListener("keydown", handler, true);
      });
    }

    function checkShortcutMatch(e, shortcutString) {
      const parts = shortcutString.split("+").map(p => p.trim());
      let requiredModifiers = { Control: false, Shift: false, Alt: false, Command: false };
      let requiredKey = "";
      parts.forEach(part => {
        if (["Control", "Shift", "Alt", "Command"].includes(part)) {
          requiredModifiers[part] = true;
        } else {
          requiredKey = part.toLowerCase();
        }
      });
      if (requiredModifiers.Control !== e.ctrlKey) return false;
      if (requiredModifiers.Shift !== e.shiftKey) return false;
      if (requiredModifiers.Alt !== e.altKey) return false;
      if (requiredModifiers.Command !== e.metaKey) return false;
      if (requiredKey && e.key.toLowerCase() !== requiredKey) return false;
      return true;
    }

    // Функция навигации между папками
    function navigateFolder(direction) {
      const folders = Array.from(document.querySelectorAll('.menu-horizontal-div-item.rp'));
      if (!folders.length) {
        console.warn("Папки не найдены");
        return;
      }
      const activeIndex = folders.findIndex(folder => folder.classList.contains('active'));
      if (activeIndex === -1) {
        console.warn("Активная папка не найдена");
        return;
      }
      let newIndex = activeIndex + direction;
      if (newIndex >= folders.length) newIndex = 0;
      if (newIndex < 0) newIndex = folders.length - 1;
      const newFolder = folders[newIndex];
      if (newFolder) {
        const clickEvent = new MouseEvent("click", {
          view: window,
          bubbles: true,
          cancelable: true
        });
        newFolder.dispatchEvent(clickEvent);
        logger.info("Переключение папки: новый индекс", newIndex);
      } else {
        console.warn("Новая папка не найдена");
      }
    }

    // Тултип для кнопки "Улучшить текст"
    function initTooltip() {
      const improveTextBtn = document.getElementById("improveTextBtn");
      if (improveTextBtn) {
        const tooltip = document.createElement("div");
        tooltip.style.position = "fixed";
        tooltip.style.backgroundColor = "rgba(0, 0, 0, 0.8)";
        tooltip.style.color = "#fff";
        tooltip.style.padding = "5px 10px";
        tooltip.style.borderRadius = "3px";
        tooltip.style.fontSize = "12px";
        tooltip.style.pointerEvents = "none";
        tooltip.style.opacity = "0";
        tooltip.style.transition = "opacity 0.2s ease";
        tooltip.style.zIndex = "11001";
        tooltip.innerText = "Command+J: Улучшить текст";
        document.body.appendChild(tooltip);

        improveTextBtn.addEventListener("mouseenter", () => {
          const rect = improveTextBtn.getBoundingClientRect();
          const tooltipHeight = tooltip.offsetHeight;
          tooltip.style.left = rect.left + "px";
          tooltip.style.top = (rect.top - tooltipHeight - 5) + "px";
          tooltip.style.opacity = "1";
        });
        improveTextBtn.addEventListener("mouseleave", () => {
          tooltip.style.opacity = "0";
        });
      }
    }
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", initTooltip);
    } else {
      initTooltip();
    }



    (function addSideDragHandles() {
      const panel = document.getElementById("telegram-logger-panel");
      if (!panel) return;
      const leftDragHandle = document.createElement("div");
      leftDragHandle.className = "left-drag-handle drag-handle";
      leftDragHandle.style.position = "absolute";
      leftDragHandle.style.left = "0";
      leftDragHandle.style.top = "0";
      leftDragHandle.style.width = "10px";
      leftDragHandle.style.height = "100%";
      leftDragHandle.style.cursor = "move";
      leftDragHandle.style.zIndex = "11001";
      leftDragHandle.style.backgroundColor = "transparent";
      leftDragHandle.addEventListener("mouseenter", () => {
        leftDragHandle.style.backgroundColor = "rgba(0, 0, 0, 0.1)";
      });
      leftDragHandle.addEventListener("mouseleave", () => {
        leftDragHandle.style.backgroundColor = "transparent";
      });
      panel.appendChild(leftDragHandle);
      makeDraggable(leftDragHandle, panel);

      const rightDragHandle = document.createElement("div");
      rightDragHandle.className = "right-drag-handle drag-handle";
      rightDragHandle.style.position = "absolute";
      rightDragHandle.style.right = "0";
      rightDragHandle.style.top = "0";
      rightDragHandle.style.width = "10px";
      rightDragHandle.style.height = "100%";
      rightDragHandle.style.cursor = "move";
      rightDragHandle.style.zIndex = "11001";
      rightDragHandle.style.backgroundColor = "transparent";
      rightDragHandle.addEventListener("mouseenter", () => {
        rightDragHandle.style.backgroundColor = "rgba(0, 0, 0, 0.1)";
      });
      rightDragHandle.addEventListener("mouseleave", () => {
        rightDragHandle.style.backgroundColor = "transparent";
      });
      panel.appendChild(rightDragHandle);
      makeDraggable(rightDragHandle, panel);

    // Создание простого фолдаута с Command+U
    function createSimpleFoldout(selection) {
      if (!selection.rangeCount) return;

      const range = selection.getRangeAt(0);
      const content = range.toString().trim();
      const lines = content.split('\n');

      if (lines.length === 0) return;

      const title = lines[0];
      const restContent = lines.slice(1).join('\n');

      range.deleteContents();

      // Создаем текстовый узел для стрелки
      const arrow = document.createTextNode('▶ ');
      const arrowSpan = document.createElement('span');
      arrowSpan.style.color = '#ffcc00';
      arrowSpan.appendChild(arrow);

      // Создаем текстовый узел для заголовка
      const titleNode = document.createTextNode(title);

      // Создаем текстовый узел для переноса строки
      const newLine = document.createTextNode('\n');

      // Создаем текстовый узел для контента
      const contentNode = document.createTextNode(restContent);
      const contentWrapper = document.createElement('span');
      contentWrapper.style.display = 'none';
      contentWrapper.style.paddingLeft = '15px';
      contentWrapper.appendChild(contentNode);

      // Объединяем стрелку и заголовок в один кликабельный элемент
      const clickable = document.createElement('span');
      clickable.style.cursor = 'pointer';
      clickable.appendChild(arrowSpan);
      clickable.appendChild(titleNode);

      // Вставляем элементы
      range.insertNode(contentWrapper);
      range.insertNode(newLine);
      range.insertNode(clickable);

      // Добавляем обработчик клика
      clickable.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        if (contentWrapper.style.display === 'none') {
          contentWrapper.style.display = 'block';
          arrowSpan.textContent = '▼ ';
        } else {
          contentWrapper.style.display = 'none';
          arrowSpan.textContent = '▶ ';
        }
      });

      // Сохраняем изменения
      const chatNote = document.getElementById('chatNote');
      if (chatNote) {
        chatNote.dispatchEvent(new Event('input', { bubbles: true }));
        setTimeout(() => chatNote.dispatchEvent(new Event('blur')), 100);
      }

      showPopup('Фолдаут создан');
    }
  })();

    const loggerPanel = document.getElementById("telegram-logger-panel");
    const chatNote = document.getElementById("chatNote");
    let originalPanelHeight = loggerPanel.offsetHeight;
    let originalNoteHeight = chatNote.offsetHeight;
    let persistentExpandEnabled = false;
    const expansionExtra = 250;
    let isEditing = false;

    function checkShortcutMatch(e, shortcutString) {
      const parts = shortcutString.split("+").map(part => part.trim().toLowerCase());
      const ctrl = parts.includes("control");
      const shift = parts.includes("shift");
      const alt = parts.includes("alt");
      const command = parts.includes("command");
      if (ctrl !== e.ctrlKey) return false;
      if (shift !== e.shiftKey) return false;
      if (alt !== e.altKey) return false;
      if (command !== e.metaKey) return false;
      const keys = parts.filter(part => !["control", "shift", "alt", "command"].includes(part));
      if (keys.length === 0) return true;
      return keys[0] === e.key.toLowerCase();
    }




    document.addEventListener("keydown", (e) => {
      if (document.activeElement && document.activeElement.id === "chatNote") {
        const toggleShortcut = localStorage.getItem("shortcut-toggleExpand") || "Command+O";
        if (checkShortcutMatch(e, toggleShortcut)) {
        e.preventDefault();
          persistentExpandEnabled = !persistentExpandEnabled;
          if (persistentExpandEnabled) {
            loggerPanel.style.height = (originalPanelHeight + expansionExtra) + "px";
            chatNote.style.height = (originalNoteHeight + expansionExtra) + "px";
            showPopup("Режим фиксированного расширения включён");
          } else {
            loggerPanel.style.height = originalPanelHeight + "px";
            chatNote.style.height = originalNoteHeight + "px";
            showPopup("Режим фиксированного расширения выключён");
          }
        }
      }
    });

    const observer = new ResizeObserver(entries => {
      for (let entry of entries) {
        if (entry.target === loggerPanel) {
          const newWidth = loggerPanel.clientWidth - 20;
          chatNote.style.width = newWidth + "px";
        }
      }
    });
    observer.observe(loggerPanel);

    function closeApp() {
      const mainPanel = document.getElementById("telegram-logger-panel");
      if (mainPanel) {
        mainPanel.style.display = "none";
      }
    }

    function openApp() {
      const mainPanel = document.getElementById("telegram-logger-panel");
      if (mainPanel) {
        mainPanel.style.display = "flex";
      }
    }

    function toggleApp() {
      const mainPanel = document.getElementById("telegram-logger-panel");
      if (mainPanel) {
        if (mainPanel.style.display === "none" || mainPanel.style.display === "") {
          openApp();
        } else {
          closeApp();
        }
      }
    }
    const closeBtn = document.getElementById("close-app");
    if (closeBtn) {
      closeBtn.addEventListener("click", closeApp);
    }

    document.addEventListener("keydown", (e) => {
      const closeShortcut = localStorage.getItem("shortcut-closeApp") || "Command+X";
      if (checkShortcutMatch(e, closeShortcut)) {
        e.preventDefault();
        toggleApp();
      }
    });


    document.getElementById("backupNotes").addEventListener("click", () => {
      console.log("Backup process started.");
      let backupText = "";
      const chatNotePrefix = "telegram_chat_note_";
      const globalNotePrefix = "telegram_general_notes_";
      const globalTabs = ['main', 'backlog', 'routine', 'mini-projects'];

      let globalNotesContent = "";
      let globalNotesFound = false;
      console.log("Checking for global notes...");
      globalTabs.forEach(tabId => {
        const key = `${globalNotePrefix}${tabId}`;
        console.log(`Checking key: ${key}`);
        const noteHTML = localStorage.getItem(key);
        if (noteHTML !== null && noteHTML.trim() !== '') {
          globalNotesFound = true;
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = noteHTML;
          const noteText = tempDiv.innerText.trim();
          console.log(`Found note for ${key}:`, noteText.substring(0, 100) + (noteText.length > 100 ? '...' : ''));

          globalNotesContent += `### Tab: ${tabId} ###\n\n${noteText}\n\n--------\n\n`;
        } else {
            console.log(`No note found or note is empty for key: ${key}`);
        }
      });
      console.log("Finished checking global notes. Found:", globalNotesFound);

      // Получаем данные о рядах и карточках
      let rowsContent = "";
      let rowsFound = false;

      try {
        console.log("Getting row and card data...");

        // Получаем конфигурацию рядов
        const rowsConfig = JSON.parse(localStorage.getItem('all_rows_config') || '[]');
        // Получаем избранные чаты
        const favoriteChats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
        // Получаем распределение карточек по рядам
        const cardRowAssignments = JSON.parse(localStorage.getItem('card_row_assignments') || '{}');

        if (rowsConfig.length > 0 && favoriteChats.length > 0) {
          rowsFound = true;
          rowsContent += "### Rows Configuration ###\n\n";

          // Добавляем информацию о каждом ряде
          rowsConfig.forEach(row => {
            rowsContent += `Row: ${row.title} (ID: ${row.id})\n`;
            rowsContent += `Expanded: ${row.expanded ? 'Yes' : 'No'}\n\n`;

            // Находим карточки для этого ряда
            const rowCards = favoriteChats.filter(chat =>
              cardRowAssignments[chat.id] === row.id
            );

            if (rowCards.length > 0) {
              rowsContent += "Cards in this row:\n";
              rowCards.forEach((card, idx) => {
                rowsContent += `  ${idx+1}. ${card.name} (ID: ${card.id})\n`;

                // Получаем заметку для этой карточки, если есть
                const cardNoteKey = `${chatNotePrefix}${card.id}`;
                const cardNoteHTML = localStorage.getItem(cardNoteKey);

                if (cardNoteHTML && cardNoteHTML.trim() !== '') {
                  const tempDiv = document.createElement('div');
                  tempDiv.innerHTML = cardNoteHTML;
                  const cardNoteText = tempDiv.innerText.trim();

                  if (cardNoteText) {
                    rowsContent += `     Note: ${cardNoteText.replace(/\n/g, '\n     ')}\n`;
                  }
                }
              });
              rowsContent += "\n";
            } else {
              rowsContent += "No cards in this row\n\n";
            }

            rowsContent += "--------\n\n";
          });
        }
      } catch (error) {
        console.error("Error processing rows and cards:", error);
      }

      let chatNotesContent = "";
      let chatNotesFound = false;
      const processedChatNoteKeys = new Set();
      console.log("Checking for chat notes...");

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key.startsWith(chatNotePrefix) && !processedChatNoteKeys.has(key)) {
          const currentChatId = key.slice(chatNotePrefix.length);
          const noteHTML = localStorage.getItem(key);
          if (noteHTML !== null && noteHTML.trim() !== '') {
            chatNotesFound = true;
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = noteHTML;
            const noteText = tempDiv.innerText.trim();
            console.log(`Found chat note for ${currentChatId}:`, noteText.substring(0, 100) + (noteText.length > 100 ? '...' : ''));
            chatNotesContent += `@${currentChatId}\n\n${noteText}\n\n--------\n\n`;
          }
          processedChatNoteKeys.add(key);
        }
      }
      console.log("Finished checking chat notes. Found:", chatNotesFound);

      if (globalNotesFound) {
        backupText += "## Global Notes ##\n\n";
        backupText += globalNotesContent.replace(/--------\n\n$/, '').trim();
        console.log("Added global notes section to backup.");
      }

      if (rowsFound) {
        if (backupText) {
          backupText += "\n\n\n";
        }
        backupText += "## Rows and Cards ##\n\n";
        backupText += rowsContent.replace(/--------\n\n$/, '').trim();
        console.log("Added rows and cards section to backup.");
      }

      if (chatNotesFound) {
        if (backupText) {
          backupText += "\n\n\n";
        }
        backupText += "## Chat Notes ##\n\n";
        backupText += chatNotesContent.replace(/--------\n\n$/, '').trim();
        console.log("Added chat notes section to backup.");
      }

      if (!globalNotesFound && !chatNotesFound && !rowsFound) {
        console.log("No notes or cards found for backup.");
        showPopup("Нет заметок и карточек для бекапа");
      } else {
        backupText = backupText.trim();
        console.log("Final backup text length:", backupText.length);

        const blob = new Blob([backupText.replace(/\n/g, '\r\n')], { type: 'text/plain;charset=utf-8;' });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", "backup-notes.txt");
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        console.log("Backup file download initiated.");
        showPopup("Бекап заметок и карточек сохранён");
      }
    });

    // Функция для импорта заметок из текстового файла
    function importNotesFromFile(fileContent) {
      console.log("Import process started.");
      const chatNotePrefix = "telegram_chat_note_";
      const globalNotePrefix = "telegram_general_notes_";

      // Разделяем контент на секции
      const sections = {};
      let currentSection = null;
      let currentSectionContent = "";

      // Парсим содержимое файла на разделы
      fileContent.split(/\r?\n/).forEach(line => {
        // Определение секций верхнего уровня
        if (line.startsWith("## Global Notes ##")) {
          currentSection = "globalNotes";
          currentSectionContent = "";
        } else if (line.startsWith("## Rows and Cards ##")) {
          currentSection = "rowsCards";
          currentSectionContent = "";
        } else if (line.startsWith("## Chat Notes ##")) {
          currentSection = "chatNotes";
          currentSectionContent = "";
        } else if (currentSection) {
          // Добавляем содержимое к текущему разделу
          currentSectionContent += line + "\n";
          sections[currentSection] = currentSectionContent;
        }
      });

      let importSuccesses = 0;
      let importErrors = 0;

      // Обрабатываем глобальные заметки
      if (sections.globalNotes) {
        try {
          console.log("Processing global notes...");
          // Разбираем на отдельные заметки для каждой вкладки
          const tabMatches = sections.globalNotes.matchAll(/### Tab: ([a-zA-Z-]+) ###\s*\n\n([\s\S]*?)(?=\n\n--------\n\n|$)/g);
          for (const match of tabMatches) {
            const tabId = match[1];
            const noteContent = match[2].trim();

            if (tabId && noteContent) {
              // Добавляем HTML-обертку для сохранения заметки
              const noteWithHtml = `<!--HTML_CONTENT_START-->${noteContent}<!--HTML_CONTENT_END-->`;
              const key = `${globalNotePrefix}${tabId}`;
              localStorage.setItem(key, noteWithHtml);
              console.log(`Imported global note for tab: ${tabId}`);
              importSuccesses++;
            }
          }
        } catch (error) {
          console.error("Error importing global notes:", error);
          importErrors++;
        }
      }

      // Обрабатываем заметки чатов
      if (sections.chatNotes) {
        try {
          console.log("Processing chat notes...");
          // Разбираем на отдельные заметки для каждого чата
          const chatMatches = sections.chatNotes.matchAll(/@([^\n]+)\s*\n\n([\s\S]*?)(?=\n\n--------\n\n|$)/g);
          for (const match of chatMatches) {
            const chatId = match[1];
            const noteContent = match[2].trim();

            if (chatId && noteContent) {
              // Добавляем HTML-обертку для сохранения заметки
              const noteWithHtml = `<!--HTML_CONTENT_START-->${noteContent}<!--HTML_CONTENT_END-->`;
              const key = `${chatNotePrefix}${chatId}`;
              localStorage.setItem(key, noteWithHtml);
              console.log(`Imported chat note for: ${chatId}`);
              importSuccesses++;
            }
          }
        } catch (error) {
          console.error("Error importing chat notes:", error);
          importErrors++;
        }
      }

      // Выводим результат
      if (importSuccesses > 0) {
        showPopup(`Импортировано заметок: ${importSuccesses}${importErrors ? `, ошибок: ${importErrors}` : ''}`);
        // Обновляем текущую заметку, если мы на вкладке заметок
        if (document.getElementById("tab-notes-content").classList.contains("active")) {
          const savedNote = loadNote(noteCookieName) || "";
          const chatNoteDiv = document.getElementById("chatNote");
          if (savedNote.includes('<!--HTML_CONTENT_START-->') && savedNote.includes('<!--HTML_CONTENT_END-->')) {
            let htmlContent = savedNote.replace(/<!--HTML_CONTENT_START-->/, '')
                              .replace(/<!--HTML_CONTENT_END-->$/, '');
            chatNoteDiv.innerHTML = htmlContent;
          } else {
            chatNoteDiv.innerText = savedNote;
          }
          activateFoldouts(chatNoteDiv);
        }
      } else {
        showPopup("Не удалось импортировать заметки. Проверьте формат файла.");
      }
    }

    // Добавляем кнопку импорта заметок в интерфейс
    function setupImportNotesButton() {
      // Создаем скрытый input для выбора файла
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = '.txt';
      fileInput.id = 'notesImportInput';
      fileInput.style.display = 'none';
      document.body.appendChild(fileInput);

      // Создаем кнопку импорта и вставляем её после кнопки бекапа
      const backupButton = document.getElementById('backupNotes');
      if (backupButton) {
        const importButton = document.createElement('button');
        importButton.id = 'importNotes';
        importButton.textContent = 'Импортировать заметки';
        importButton.classList = backupButton.classList; // Копируем стили
        importButton.style.marginTop = '10px';
        backupButton.insertAdjacentElement('afterend', importButton);

        // Обработчик клика на кнопку импорта
        importButton.addEventListener('click', () => {
          fileInput.click();
        });

        // Обработчик выбора файла
        fileInput.addEventListener('change', (e) => {
          const file = e.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = (event) => {
              const fileContent = event.target.result;
              importNotesFromFile(fileContent);
              // Сбрасываем значение input, чтобы можно было выбрать тот же файл снова
              fileInput.value = '';
            };
            reader.readAsText(file, 'UTF-8');
          }
        });
      }
    }

    // Вызываем функцию настройки кнопки импорта при инициализации
    setupImportNotesButton();

    // Мультивыбор сообщений
    let multiSelectedMessages = [];
    const multiSelectionContainer = document.createElement('div');
    multiSelectionContainer.id = 'multi-selection-container';
    multiSelectionContainer.style.position = 'fixed';
    multiSelectionContainer.style.display = 'none';
    multiSelectionContainer.style.backgroundColor = '#1A1A1A';
    multiSelectionContainer.style.borderRadius = '20px';
    multiSelectionContainer.style.zIndex = '11000';
    multiSelectionContainer.style.alignItems = 'center';
    multiSelectionContainer.style.gap = '8px';
    multiSelectionContainer.style.padding = '10px 15px';
    multiSelectionContainer.style.boxShadow = '0px 4px 10px rgba(0, 0, 0, 0.3)';
    multiSelectionContainer.style.opacity = '92%';
    multiSelectionContainer.innerHTML = `
      <button id="emojiButton" style="border: none; background: none; font-size: 20px;">🤖</button>
      <input id="multiPromptInput" type="text" placeholder="Введите промпт..."
        style="flex-grow: 1; height: 40px; padding: 6px; border: none; border-radius: 20px; background: none; color: white; font-size: 16px; outline: none;">
      <button id="multiPromptSubmit" style="height: 40px; padding: 6px 12px; font-size: 14px; background: none; color: white;">Отправить</button>
      <button id="multiPromptForward" style="height: 40px; padding: 6px 12px; font-size: 14px; background: none; color: white;">Переслать</button>
      <button id="multiPromptCancel" style="height: 40px; padding: 6px 12px; font-size: 14px; background: none; color: white;">Отмена</button>
    `;
    document.body.appendChild(multiSelectionContainer);

    function positionMultiSelectionContainer() {
      const telegramInput = document.querySelector('div.input-message-input[contenteditable="true"]');
      if (telegramInput) {
        const rect = telegramInput.getBoundingClientRect();
        logger.info("Telegram Input найден:", rect);
        multiSelectionContainer.style.left = `${rect.left}px`;
        multiSelectionContainer.style.top = `${rect.top}px`;
        multiSelectionContainer.style.width = `${rect.width + 50}px`;
        multiSelectionContainer.style.height = `${rect.height}px`;
      } else {
        console.warn("Поле ввода Telegram не найдено! Задаю позицию по умолчанию.");
        multiSelectionContainer.style.left = '20px';
        multiSelectionContainer.style.top = '20px';
        multiSelectionContainer.style.width = '350px';
        multiSelectionContainer.style.height = 'auto';
      }
    }

    document.addEventListener("DOMContentLoaded", positionMultiSelectionContainer);
    window.addEventListener('resize', positionMultiSelectionContainer);

    document.getElementById('multiPromptInput').addEventListener('keydown', function(e) {
      if (e.key === "Enter") {
        e.preventDefault();
        document.getElementById('multiPromptSubmit').click();
      }
    });

    const multiSelectStyle = document.createElement('style');
    multiSelectStyle.innerHTML = `
      .multi-select-overlay {
        position: absolute;
        top: 5px;
        left: 5px;
        background-color: #007bff;
        color: #fff;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        font-weight: bold;
        pointer-events: none;
        z-index: 10;
      }
      .multi-selected {
        outline: 2px solid #007bff;
        position: relative;
      }
      .start-selected {
        border: 2px solid #ff9900;
        border-radius: 4px;
        padding: 2px;
      }
      .end-selected {
        border: 2px solid #00ff99;
        border-radius: 4px;
        padding: 2px;
      }
    `;
    document.head.appendChild(multiSelectStyle);

    function updateMultiSelectOverlays() {
      const currentChat = chatId;
      document.querySelectorAll('.bubble .message').forEach(msgElem => {
        const bubble = msgElem.closest('.bubble');
        const mid = bubble.getAttribute('data-mid');
        const isSelected = multiSelectedMessages.some(item => item.chatId === currentChat && item.mid === mid);
        if (isSelected) {
          if (!msgElem.classList.contains('multi-selected')) {
            msgElem.classList.add('multi-selected');
          }
          let overlay = msgElem.querySelector('.multi-select-overlay');
          if (!overlay) {
            overlay = document.createElement('span');
            overlay.className = 'multi-select-overlay';
            msgElem.appendChild(overlay);
          }
          const selectedItems = multiSelectedMessages.filter(item => item.chatId === currentChat);
          const index = selectedItems.findIndex(item => item.mid === mid);
          overlay.textContent = index + 1;
        } else {
          msgElem.classList.remove('multi-selected');
          const overlay = msgElem.querySelector('.multi-select-overlay');
          if (overlay) {
            overlay.remove();
          }
        }
      });
    }

    function handleMultiSelectMessage(msgElem) {
      const currentChat = chatId;
      const bubble = msgElem.closest('.bubble');
      const mid = bubble.getAttribute('data-mid');
      const found = multiSelectedMessages.find(item => item.chatId === currentChat && item.mid === mid);
      if (found) {
        multiSelectedMessages = multiSelectedMessages.filter(item => !(item.chatId === currentChat && item.mid === mid));
        msgElem.classList.remove('multi-selected');
        const overlay = msgElem.querySelector('.multi-select-overlay');
        if (overlay) overlay.remove();

        logger.info('Сообщение исключено из мультивыбора', { текст: msgElem.innerText.slice(0,50) + '...' });
      } else {
        msgElem.classList.add('multi-selected');
        multiSelectedMessages.push({ chatId: currentChat, mid: mid, text: msgElem.innerText.trim() });

        logger.info('Сообщение добавлено в мультивыбор', { id: mid, текст: msgElem.innerText.slice(0,50) + '...' });
      }
      updateMultiSelectOverlays();
      if (multiSelectedMessages.length > 0) {
        multiSelectionContainer.style.display = 'flex';
        positionMultiSelectionContainer();
        const multiPromptInput = document.getElementById('multiPromptInput');
        if (!multiPromptInput.value) {
          const lastUserPrompt = localStorage.getItem('lastUserPrompt');
          if (lastUserPrompt) {
            multiPromptInput.value = lastUserPrompt;
          }
        }
      } else {
        multiSelectionContainer.style.display = 'none';
      }
    }

    document.addEventListener('click', function(e) {
      const msgElem = e.target.closest('.bubble .message');
      if (msgElem && (e.metaKey || e.ctrlKey)) {
        e.stopPropagation();
        e.preventDefault();
        handleMultiSelectMessage(msgElem);
      }
    }, true);

    document.getElementById('multiPromptSubmit').addEventListener('click', async () => {
      const promptInput = document.getElementById('multiPromptInput');
      const userPrompt = promptInput.value.trim();

      if (!userPrompt) {
        showPopup("Пожалуйста, введите запрос");
        return;
      }
      if (multiSelectedMessages.length === 0) {
        showPopup("Нет выбранных сообщений для обработки");
        return;
      }

      // Сразу показываем попап и прячем контейнер мультивыбора,
      // чтобы инпут исчез мгновенно
      showPopup("Генерация ответа AI...", true);
      multiSelectionContainer.style.display = 'none';

      // Сохраняем последний промпт (если у вас есть такая логика)
      localStorage.setItem("lastUserPrompt", userPrompt);

      try {
        // Формируем данные для отправки
        const formattedMessages = multiSelectedMessages
        .filter(item => item.chatId === chatId)
        .map((item, index) => `${index + 1}. ${item.text}`)
        .join("\n");

        const structuredPrompt = `<system_instruction>
          промпт: ${userPrompt}
          </system_instruction>
          <входные_данные>
          ${formattedMessages}
          </входные_данные>`;

        logger.info('Запрос (мультивыбор):', { текст: structuredPrompt });

      const selectedModel = localStorage.getItem("selectedModel") || "gpt-4.1-mini";
        const API_URL = "https://api.openai.com/v1/chat/completions";
        const apiToken = "********************************************************************************************************************************************************************";

      const payload = {
        model: selectedModel,
        messages: [
            { role: 'user', content: structuredPrompt }
          ]
        };

        // Запрос к API
        const response = await fetch(API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + apiToken
          },
          body: JSON.stringify(payload)
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Ошибка API: ${response.status}. ${errorText}`);
        }

        const data = await response.json();
        logger.info("Ответ API (мультивыбор):", data);

        if (data.choices && data.choices[0] && data.choices[0].message) {
          const improvedText = data.choices[0].message.content.trim();
          if (currentInputField) {
            // Вставляем текст в поле ввода
            currentInputField.innerText = improvedText;
            showPopup("Ответ получен и вставлен в поле ввода!");
          } else {
            showPopup("Поле ввода не найдено");
          }
        } else {
          throw new Error("Неверный формат ответа от API");
        }
      } catch (error) {
        logger.error("Ошибка при обработке мультивыбора:", error);
        hidePopup(); // Hide the persistent popup
        showPopup("Ошибка генерации ответа");
      } finally {
        clearMultiSelection();
      }
    });

    document.getElementById('multiPromptCancel').addEventListener('click', () => {
      clearMultiSelection();
      showPopup('Мультивыбор отменён');
    });

    // Добавляем обработчик для кнопки "Переслать"
    document.getElementById('multiPromptForward').addEventListener('click', (e) => {
      if (multiSelectedMessages.length > 0) {
        // Берем первое сообщение только для передачи DOM-элемента,
        // сами сообщения будут обработаны в showFavoriteChatsMenu
        const firstMsgId = multiSelectedMessages[0].mid;
        const firstMsg = document.querySelector(`.bubble[data-mid="${firstMsgId}"] .message`);

        if (firstMsg) {
          // Вызываем функцию showFavoriteChatsMenu с координатами кнопки
          const buttonRect = e.target.getBoundingClientRect();
          const menuEvent = {
            clientX: buttonRect.left + buttonRect.width/2,
            clientY: buttonRect.top
          };
          showFavoriteChatsMenu(menuEvent, firstMsg);

          // Выводим сообщение для отладки
          showPopup('Открываю меню избранных чатов...');
          logger.info('Нажата кнопка "Переслать", координаты:', menuEvent);
        } else {
          showPopup('Не удалось найти выбранное сообщение');
        }
      } else {
        showPopup('Нет выбранных сообщений для пересылки');
      }
    });

    function clearMultiSelection() {
      multiSelectedMessages.forEach(item => {
        if (item.chatId === chatId) {
          const msgElem = document.querySelector(`.bubble[data-mid="${item.mid}"] .message`);
          if (msgElem) {
            msgElem.classList.remove('multi-selected');
            const overlay = msgElem.querySelector('.multi-select-overlay');
            if (overlay) overlay.remove();

            // Удаляем кнопку пересылки
            const forwardBtn = msgElem.querySelector('.super-favorite-forward-btn');
            if (forwardBtn) forwardBtn.remove();
          }
        }
      });
      multiSelectedMessages = [];
      multiSelectionContainer.style.display = 'none';
      const multiPromptInput = document.getElementById('multiPromptInput');
      if(multiPromptInput) multiPromptInput.value = '';

      // Дополнительная очистка для всех сообщений на странице (на случай, если какие-то иконки остались)
      document.querySelectorAll('.super-favorite-forward-btn').forEach(btn => btn.remove());

      logger.info('Мультивыбор очищен');
    }

    document.addEventListener("keydown", (e) => {
      const shortcutImprove = localStorage.getItem("shortcut-improveText") || "Command+J";
      if (checkShortcutMatch(e, shortcutImprove)) {
        e.preventDefault();
        if (!isImprovementProcessing) {
          isImprovementProcessing = true;
          showPopup("Запуск улучшения текста");
        const improveBtn = document.getElementById("improveTextBtn");
        if (improveBtn) {
          improveBtn.click();
          }
          setTimeout(() => { isImprovementProcessing = false; }, 1000);
        }
      }

      const shortcutSwitchLeft = localStorage.getItem("shortcut-switchLeft") || "Control+ArrowLeft";
      if (checkShortcutMatch(e, shortcutSwitchLeft)) {
        e.preventDefault();
        navigateFolder(-1);
      }

      const shortcutSwitchRight = localStorage.getItem("shortcut-switchRight") || "Control+ArrowRight";
      if (checkShortcutMatch(e, shortcutSwitchRight)) {
        e.preventDefault();
        navigateFolder(1);
      }

      const shortcutSearch = localStorage.getItem("shortcut-search") || "Command+K";
      if (checkShortcutMatch(e, shortcutSearch)) {
        e.preventDefault();
        const searchContainer = document.querySelector('.input-search');
        if (searchContainer) {
          const clickEvent = new MouseEvent("click", {
            view: window,
            bubbles: true,
            cancelable: true
          });
          searchContainer.dispatchEvent(clickEvent);
          const searchInput = searchContainer.querySelector('.input-search-input');
          if (searchInput) {
            searchInput.focus();
          }
        }
      }
    });


    // быстрые сообщения
    const quickMessages = {
      getGlobal: () => JSON.parse(localStorage.getItem('quickMessages_global') || '[]'),
      setGlobal: (messages) => localStorage.setItem('quickMessages_global', JSON.stringify(messages)),
      getForChat: (chatId) => JSON.parse(localStorage.getItem(`quickMessages_${chatId}`) || '[]'),
      setForChat: (chatId, messages) => localStorage.setItem(`quickMessages_${chatId}`, JSON.stringify(messages))
    };

    function renderQuickMessage(message, index, container, isGlobal = true) {
      const item = document.createElement('div');
      item.className = 'quick-message-item';
      item.style.fontSize = '12px';
      item.innerHTML = `
        <span class="quick-message-text" style="font-size: 12px">${message}</span>
        <div class="quick-message-actions">
          <button class="quick-message-button" title="Удалить">🗑️</button>
        </div>
      `;

      item.querySelector('.quick-message-button').addEventListener('click', () => {
        const messages = isGlobal ? quickMessages.getGlobal() : quickMessages.getForChat(chatId);
        messages.splice(index, 1);
        if (isGlobal) {
          quickMessages.setGlobal(messages);
          updateGlobalQuickMessages();
        } else {
          quickMessages.setForChat(chatId, messages);
          updateChatQuickMessages();
        }
        // Обновляем весь интерфейс быстрых сообщений
        updateQuickMessagesUI();
      });

      item.addEventListener('click', (e) => {
        if (!e.target.closest('.quick-message-button')) {
          if (currentInputField) {
            currentInputField.innerText = message;
            // Устанавливаем курсор в конец текста
            const range = document.createRange();
            const sel = window.getSelection();
            range.selectNodeContents(currentInputField);
            range.collapse(false);
            sel.removeAllRanges();
            sel.addRange(range);
            showPopup('Быстрое сообщение вставлено');
          }
        }
      });

      container.appendChild(item);
    }

    function updateGlobalQuickMessages() {
      const container = document.getElementById('global-quick-messages');
      container.innerHTML = '';
      const messages = quickMessages.getGlobal();
      messages.forEach((msg, idx) => renderQuickMessage(msg, idx, container, true));
    }

    function updateChatQuickMessages() {
      const container = document.getElementById('chat-quick-messages');
      container.innerHTML = '';
      const messages = quickMessages.getForChat(chatId);
      messages.forEach((msg, idx) => renderQuickMessage(msg, idx, container, false));
    }

    document.getElementById('global-quick-message-submit').addEventListener('click', () => {
      const input = document.getElementById('global-quick-message-input');
      const message = input.value.trim();
      if (message) {
        const messages = quickMessages.getGlobal();
        messages.push(message);
        quickMessages.setGlobal(messages);
        input.value = '';
        updateGlobalQuickMessages();
        showPopup('Общее быстрое сообщение добавлено');
      }
    });

    document.getElementById('chat-quick-message-submit').addEventListener('click', () => {
      const input = document.getElementById('chat-quick-message-input');
      const message = input.value.trim();
      if (message) {
        const messages = quickMessages.getForChat(chatId);
        messages.push(message);
        quickMessages.setForChat(chatId, messages);
        input.value = '';
        updateChatQuickMessages();
        showPopup('Быстрое сообщение для чата добавлено');
      }
    });

    document.addEventListener('input', (e) => {
      if (e.target === currentInputField && e.target.innerText === '>') {
        const popup = document.getElementById('quick-messages-popup');
        const rect = currentInputField.getBoundingClientRect();

        popup.style.left = `${rect.left}px`;
        popup.style.bottom = `${window.innerHeight - rect.top + 5}px`;

        const globalMessages = quickMessages.getGlobal();
        const chatMessages = quickMessages.getForChat(chatId);

        popup.innerHTML = '';

        if (globalMessages.length || chatMessages.length) {
          if (chatMessages.length) {
            const chatHeader = document.createElement('div');
            chatHeader.className = 'quick-messages-popup-item';
            chatHeader.style.fontWeight = 'bold';
            chatHeader.style.color = '#2e2e2e';
            chatHeader.textContent = 'Сообщения чата:';
            popup.appendChild(chatHeader);

            chatMessages.forEach(msg => {
              const item = document.createElement('div');
              item.className = 'quick-messages-popup-item';
              item.textContent = msg;
              item.addEventListener('click', () => {
                currentInputField.innerText = msg;
                popup.classList.remove('show');
              });
              popup.appendChild(item);
            });
          }

          if (globalMessages.length) {
            const globalHeader = document.createElement('div');
            globalHeader.className = 'quick-messages-popup-item';
            globalHeader.style.fontWeight = 'bold';
            globalHeader.style.color = '#2e2e2e';
            globalHeader.textContent = 'Общие сообщения:';
            popup.appendChild(globalHeader);

            globalMessages.forEach(msg => {
              const item = document.createElement('div');
              item.className = 'quick-messages-popup-item';
              item.textContent = msg;
              item.addEventListener('click', () => {
                currentInputField.innerText = msg;
                popup.classList.remove('show');
              });
              popup.appendChild(item);
            });
          }
    } else {
          const empty = document.createElement('div');
          empty.className = 'quick-messages-popup-item';
          empty.textContent = 'Нет сохраненных сообщений';
          popup.appendChild(empty);
        }

        popup.classList.add('show');
      }
    });

    document.addEventListener('click', (e) => {
      const popup = document.getElementById('quick-messages-popup');
      if (!e.target.closest('#quick-messages-popup') && !e.target.closest('.input-message-input')) {
        popup.classList.remove('show');
      }
    });

    tabQuickBtn.addEventListener("click", () => {
      activateTab(tabQuickBtn, tabQuickContent);
      updateGlobalQuickMessages();
      updateChatQuickMessages();

      // Обновляем заголовок и плейсхолдер для текущего чата
      const chatQuickMessagesSection = document.querySelector('.quick-messages-section:nth-child(2) h3');
      if (chatQuickMessagesSection) {
          chatQuickMessagesSection.textContent = `Быстрые сообщения для чата`;
      }

      const chatQuickMessageInput = document.getElementById('chat-quick-message-input');
      if (chatQuickMessageInput) {
          chatQuickMessageInput.placeholder = `Введите быстрое сообщение для чата ${chatId}...`;
      }
    });

    function activateTab(tabButton, tabContent) {
      [tabLogBtn, tabNotesBtn, tabSettingsBtn, tabQuickBtn].forEach(btn => btn.classList.remove("active"));
      [tabLogContent, tabNotesContent, tabSettingsContent, tabQuickContent].forEach(div => div.classList.remove("active"));
      tabButton.classList.add("active");
      tabContent.classList.add("active");
    }

    document.addEventListener('input', (e) => {
      if (e.target === currentInputField) {
        const popup = document.getElementById('quick-messages-popup');
        if (!e.target.innerText.startsWith('>')) {
          popup.classList.remove('show');
          return;
        }

        if (e.target.innerText === '>') {
          const rect = currentInputField.getBoundingClientRect();
          popup.style.left = `${rect.left}px`;
          popup.style.bottom = `${window.innerHeight - rect.top + 5}px`;

          const globalMessages = quickMessages.getGlobal();
          const chatMessages = quickMessages.getForChat(chatId);

          popup.innerHTML = '';

          if (globalMessages.length || chatMessages.length) {
            if (chatMessages.length) {
              const chatHeader = document.createElement('div');
              chatHeader.className = 'quick-messages-popup-item';
              chatHeader.style.fontWeight = 'bold';
              chatHeader.style.color = '#a0a0a0';
              chatHeader.textContent = 'Сообщения чата:';
              popup.appendChild(chatHeader);

              chatMessages.forEach(msg => {
                const item = document.createElement('div');
                item.className = 'quick-messages-popup-item';
                item.textContent = msg;
                item.addEventListener('click', () => {
                  currentInputField.innerText = msg;
                  popup.classList.remove('show');
                  // Очищаем фокус со всех элементов
                  popup.querySelectorAll('.quick-messages-popup-item').forEach(item => item.classList.remove('focused'));
                });
                popup.appendChild(item);
              });
            }

            if (globalMessages.length) {
              const globalHeader = document.createElement('div');
              globalHeader.className = 'quick-messages-popup-item';
              globalHeader.style.fontWeight = 'bold';
              globalHeader.style.color = '#a0a0a0';
              globalHeader.textContent = 'Общие сообщения:';
              popup.appendChild(globalHeader);

              globalMessages.forEach(msg => {
                const item = document.createElement('div');
                item.className = 'quick-messages-popup-item';
                item.textContent = msg;
                item.addEventListener('click', () => {
                  currentInputField.innerText = msg;
                  popup.classList.remove('show');
                  // Очищаем фокус со всех элементов
                  popup.querySelectorAll('.quick-messages-popup-item').forEach(item => item.classList.remove('focused'));
                });
                popup.appendChild(item);
              });
            }
          } else {
            const empty = document.createElement('div');
            empty.className = 'quick-messages-popup-item';
            empty.textContent = 'Нет сохраненных сообщений';
            popup.appendChild(empty);
          }

          popup.classList.add('show');
        }
      }
    });

    // Закрываем попап при клике вне его
    document.addEventListener('click', (e) => {
      const popup = document.getElementById('quick-messages-popup');
      if (!e.target.closest('#quick-messages-popup') && !e.target.closest('.input-message-input')) {
        popup.classList.remove('show');
        // Очищаем фокус со всех элементов
        popup.querySelectorAll('.quick-messages-popup-item').forEach(item => item.classList.remove('focused'));
      }
    });

  function updateQuickMessagesUI() {
    console.log('Обновление интерфейса быстрых сообщений для чата:', chatId);

    updateGlobalQuickMessages();
    updateChatQuickMessages();


    const chatQuickMessagesSection = document.querySelector('#tab-quick-content .quick-messages-section:nth-child(2) h3');
    if (chatQuickMessagesSection) {
      console.log('Обновляем заголовок секции быстрых сообщений чата');
      chatQuickMessagesSection.textContent = `Быстрые сообщения для чата`;
        } else {
      console.log('Не найден элемент заголовка секции быстрых сообщений чата');
      const allHeaders = document.querySelectorAll('h3');
      for (const header of allHeaders) {
        if (header.textContent.includes('Быстрые сообщения для чата')) {
          console.log('Найден заголовок по тексту:', header.textContent);
          header.textContent = `Быстрые сообщения для чата ${chatId}`;
          break;
        }
      }
    }

    const chatQuickMessageInput = document.getElementById('chat-quick-message-input');
    if (chatQuickMessageInput) {
      console.log('Обновляем плейсхолдер поля ввода');
      chatQuickMessageInput.placeholder = `Введите быстрое сообщение для чата ${chatId}...`;
    } else {
      console.log('Не найден элемент поля ввода быстрых сообщений чата');
      const allInputs = document.querySelectorAll('input[placeholder*="Введите быстрое сообщение для чата"]');
      if (allInputs.length > 0) {
        console.log('Найдено поле ввода по placeholder');
        allInputs[0].placeholder = `Введите быстрое сообщение для чата ${chatId}...`;
      }
    }
  }



  document.getElementById('chat-quick-message-submit').addEventListener('click', () => {
    const input = document.getElementById('chat-quick-message-input');
    const message = input.value.trim();
    if (message) {
      const messages = quickMessages.getForChat(chatId);
      messages.push(message);
      quickMessages.setForChat(chatId, messages);
      input.value = '';
      updateQuickMessagesUI();
    }
  });

  // Обновляем функцию renderQuickMessage для использования updateQuickMessagesUI
  const originalRenderQuickMessage = renderQuickMessage;
  renderQuickMessage = function(message, index, container, isGlobal = true) {
    const item = document.createElement('div');
    item.className = 'quick-message-item';
    item.style.fontSize = '12px';
    item.innerHTML = `
      <span class="quick-message-text" style="font-size: 12px">${message}</span>
      <div class="quick-message-actions">
        <button class="quick-message-button" title="Удалить">🗑️</button>
      </div>
    `;

    item.querySelector('.quick-message-button').addEventListener('click', () => {
      const messages = isGlobal ? quickMessages.getGlobal() : quickMessages.getForChat(chatId);
      messages.splice(index, 1);
      if (isGlobal) {
        quickMessages.setGlobal(messages);
      } else {
        quickMessages.setForChat(chatId, messages);
      }
      updateQuickMessagesUI();
    });

    item.addEventListener('click', (e) => {
      if (!e.target.closest('.quick-message-button')) {
        if (currentInputField) {
          currentInputField.innerText = message;
          // Устанавливаем курсор в конец текста
          const range = document.createRange();
          const sel = window.getSelection();
          range.selectNodeContents(currentInputField);
          range.collapse(false);
          sel.removeAllRanges();
          sel.addRange(range);
          showPopup('Быстрое сообщение вставлено');
        }
      }
    });

    container.appendChild(item);
  };

    document.addEventListener('keydown', (e) => {
      const popup = document.getElementById('quick-messages-popup');
      if (!popup || !popup.classList.contains('show')) return;

      const items = popup.querySelectorAll('.quick-messages-popup-item:not([style*="font-weight: bold"])');
      if (!items.length) return;
      let currentIndex = Array.from(items).findIndex(item => item.classList.contains('focused'));
      if (currentIndex === -1) {
        currentIndex = items.length - 1;
        items[currentIndex].classList.add('focused');
      }

      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          e.stopPropagation();
          items[currentIndex].classList.remove('focused');
          currentIndex = (currentIndex - 1 + items.length) % items.length;
          items[currentIndex].classList.add('focused');
          items[currentIndex].scrollIntoView({ block: 'nearest' });
          break;
        case 'ArrowDown':
          e.preventDefault();
          e.stopPropagation();
          items[currentIndex].classList.remove('focused');
          currentIndex = (currentIndex + 1) % items.length;
          items[currentIndex].classList.add('focused');
          items[currentIndex].scrollIntoView({ block: 'nearest' });
          break;
        case 'Enter':
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          if (currentInputField && items[currentIndex]) {
            currentInputField.innerText = items[currentIndex].textContent;
            popup.classList.remove('show');
            currentInputField.focus();
            // Устанавливаем курсор в конец текста
            const range = document.createRange();
            const sel = window.getSelection();
            range.selectNodeContents(currentInputField);
            range.collapse(false);
            sel.removeAllRanges();
            sel.addRange(range);
            // Очищаем фокус со всех элементов
            items.forEach(item => item.classList.remove('focused'));
          }
          break;
        case 'Escape':
          e.preventDefault();
          popup.classList.remove('show');
          // Очищаем фокус со всех элементов
          items.forEach(item => item.classList.remove('focused'));
          break;
      }
    }, true);

    document.addEventListener('keypress', (e) => {
      const popup = document.getElementById('quick-messages-popup');
      if (popup && popup.style.display !== 'none' && e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
      }
    }, true);



    document.addEventListener('keydown', (e) => {
      if (checkShortcutMatch(e, 'Command+]')) {
        e.preventDefault();
        openSinglePromptUI();
      }
    });

    let singlePromptContainer = null;

    function openSinglePromptUI(initialPrompt = "", prefillWithChat = false) {
      logger.info('[AI DEBUG] openSinglePromptUI called', { initialPrompt, prefillWithChat });

      // Ensure backdrop exists and is visible
      let backdrop = document.getElementById('ai-backdrop');
      if (!backdrop) {
        logger.info('[AI DEBUG] Backdrop not found, creating...');
        backdrop = createBackdrop(); // createBackdrop should set display: 'block'
      } else {
        logger.info('[AI DEBUG] Backdrop found, ensuring display is block.');
        backdrop.style.display = 'block'; // Explicitly set display to block if it already exists
      }

      // Log backdrop state right after ensuring it's displayed
      if (backdrop) {
          logger.info('[AI DEBUG] Backdrop display style after ensuring visibility:', backdrop.style.display);
      } else {
          logger.error('[AI DEBUG] Backdrop element is unexpectedly null after creation/check.');
      }


      // Если панель уже есть, просто показываем её и фокусируемся
      if (singlePromptContainer) {
        singlePromptContainer.style.display = 'flex';
        const inputEl = document.getElementById('singlePromptInput');
        if (prefillWithChat) {
          logger.info('[AI DEBUG] Prefilling chat content');
          const currentChatMessages = chatMessages[chatId];
          if (!currentChatMessages || currentChatMessages.size === 0) {
            logger.warn('[AI DEBUG] No messages found in chatMessages[chatId] for prefill.');
            inputEl.value = initialPrompt + "\n\n--- Chat Content ---\n(No messages found in cache for this chat)";
          } else {
            const allMessagesArray = Array.from(currentChatMessages.values());
            logger.info(`[AI DEBUG] Found ${allMessagesArray.length} messages in chatMessages[${chatId}]`);
            const allMessagesText = allMessagesArray.map(msg => `${getSenderName(msg)}: ${processMessageText(msg)}`).join('\n\n');
            logger.info('[AI DEBUG] Total length of allMessagesText for prefill:', allMessagesText.length);
            if (allMessagesText.length > 30000) { // Arbitrary limit for logging, actual API limits are by token
              logger.warn('[AI DEBUG] Prefill content is very long. Truncating for input field display for stability, full content might still be used if API handles it, or might fail.');
              // Potentially truncate for display only: inputEl.value = initialPrompt + "\n\n--- Chat Content ---\n" + allMessagesText.substring(0, 29000) + "... (truncated for display)";
              inputEl.value = initialPrompt + "\n\n--- Chat Content ---\n" + allMessagesText; // Keep full for now, check console for API errors
            } else {
              inputEl.value = initialPrompt + "\n\n--- Chat Content ---\n" + allMessagesText;
            }
            // Auto-scroll to the bottom after content is loaded
            setTimeout(() => {
              inputEl.scrollTop = inputEl.scrollHeight;
            }, 0);
          }
        } else if (initialPrompt) {
          inputEl.value = initialPrompt;
        }
        inputEl.focus();

        logger.info('[AI DEBUG] singlePromptContainer displayed and focused (existing)');
        return;
      }

      logger.info('[AI DEBUG] Creating new singlePromptContainer');

      // Create backdrop element
      function createBackdrop() {
        const backdrop = document.createElement('div');
        backdrop.id = 'ai-backdrop';
        backdrop.style.position = 'fixed';
        backdrop.style.top = '0';
        backdrop.style.left = '0';
        backdrop.style.width = '100%';
        backdrop.style.height = '100%';
        backdrop.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        backdrop.style.backdropFilter = 'blur(5px)';
        backdrop.style.WebkitBackdropFilter = 'blur(5px)';
        backdrop.style.zIndex = '10999';
        backdrop.style.display = 'block';

        // Close when clicking outside
        backdrop.addEventListener('click', (e) => {
          if (e.target === backdrop) {
            logger.info('[AI DEBUG] Backdrop clicked, hiding backdrop and container.');
            backdrop.style.display = 'none';
            if (singlePromptContainer) {
              singlePromptContainer.style.display = 'none';
            }
          }
        });

        document.body.appendChild(backdrop);
        return backdrop;
      }

      singlePromptContainer = document.createElement('div');
      singlePromptContainer.id = 'singlePromptContainer';

      // Updated position styles for center positioning
      singlePromptContainer.style.position = 'fixed';
      singlePromptContainer.style.top = '50%';
      singlePromptContainer.style.left = '50%';
      singlePromptContainer.style.transform = 'translate(-50%, -50%)';
      singlePromptContainer.style.zIndex = '11000';
      singlePromptContainer.style.backgroundColor = '#1A1A1A';
      singlePromptContainer.style.borderRadius = '20px';
      singlePromptContainer.style.boxShadow = '0px 4px 10px rgba(0,0,0,0.3)';
      singlePromptContainer.style.opacity = '99%';
      singlePromptContainer.style.padding = '20px 25px';
      singlePromptContainer.style.width = '900px'; // Increased width
      singlePromptContainer.style.maxWidth = '90vw'; // Responsive max width
      singlePromptContainer.style.height = 'auto'; // Changed from fixed 600px to auto
      singlePromptContainer.style.minHeight = '450px'; // Adjusted min height
      singlePromptContainer.style.maxHeight = '80vh'; // Responsive max height
      singlePromptContainer.style.display = 'flex';
      singlePromptContainer.style.flexDirection = 'column';
      singlePromptContainer.style.alignItems = 'stretch';
      singlePromptContainer.style.gap = '12px';
      singlePromptContainer.style.overflowY = 'auto'; // Allow scrolling on the container

      // Вставляем HTML-контент
      singlePromptContainer.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
        <button id="singlePromptEmojiButton" style="border: none; background: none; font-size: 22px; color: #aaa; padding: 5px; cursor: default;">🤖</button>

        <textarea
          id="singlePromptInput"
          placeholder="Введите ваш запрос к AI..."
          style="
            flex-grow: 1;
            height: 150px; /* Reduced height */
            padding: 12px 15px;
            border: 1px solid #333;
            border-radius: 12px;
            background: #252525;
            color: white;
            font-size: 16px; /* Adjusted font size */
            outline: none;
            resize: none;
            line-height: 1.6;
            overflow-y: auto;
          "
        ></textarea>
      </div>
      <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 5px;">
        <button id="singlePromptCancel" style="height: 40px; padding: 0 20px; font-size: 15px; background: #444; border: none; border-radius: 8px; color: #ccc; cursor: pointer; transition: background-color 0.2s;">Отмена</button>
        <button id="singlePromptSend" style="height: 40px; padding: 0 25px; font-size: 15px; background: #4B6CB7; border: none; border-radius: 8px; color: white; cursor: pointer; transition: background-color 0.2s;">Отправить</button>
      </div>
      <div style="position: relative; margin-top:15px; flex-grow: 1; display: flex; flex-direction: column;">
        <div
          id="singlePromptResult"
          style="
            display: none;
            flex-grow: 1; /* Allow it to take available space */
            min-height: 150px; /* Minimum height for the result area */
            max-height: 400px; /* Adjusted max-height */
            overflow-y: auto;
            font-size: 15px; /* Adjusted font size */
            color: #ddd;
            border-top: 1px solid #333;
            padding: 30px 90px 15px 15px; /* MODIFIED: top, right, bottom, left */
            background-color: #252525;
            border-radius: 12px;
            user-select: text;
            -webkit-user-select: text;
            word-wrap: break-word;
            white-space: pre-wrap; /* Use pre-wrap for better formatting */
            overflow-x: hidden;
            line-height: 1.6;
          "
        ></div>
        <button
          id="copyResponseButton"
          style="
            display: none;
            position: absolute;
            top: 5px; /* MODIFIED */
            right: 5px; /* MODIFIED */
            background: #383838;
            color: #bbb;
            border: 1px solid #444;
            border-radius: 6px;
            padding: 6px 10px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s, color 0.2s;
            z-index: 1; /* Ensure it's above the text */
          "
        >
          📋 Копировать
        </button>
      </div>`;

      document.body.appendChild(singlePromptContainer);

      // Делаем панель перетаскиваемой (но не при клике на textarea или ответ)
      makeDraggable(singlePromptContainer);

      // Ловим textarea
      const inputEl = document.getElementById('singlePromptInput');

      // Попробуем загрузить последний запрос
      const lastRequest = localStorage.getItem('lastAskAIRequest');
      if (lastRequest) {
        inputEl.value = lastRequest;
        // Scroll to the bottom of the textarea after setting its value
        setTimeout(() => {
          inputEl.scrollTop = inputEl.scrollHeight;
        }, 0);
      }

      // Перехватываем Enter и Shift+Enter
      inputEl.addEventListener('keydown', (e) => {
        e.stopPropagation();
        // Если нажали Enter без Shift – отправляем сообщение
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendSinglePrompt();
        }
        // Если Shift+Enter – даём сделать перенос строки
      });

      // Привязываем кнопки
      document.getElementById('singlePromptSend').addEventListener('click', sendSinglePrompt);
      document.getElementById('singlePromptCancel').addEventListener('click', () => {
        logger.info('[AI DEBUG] Cancel button clicked, hiding backdrop and container.');
        if (singlePromptContainer) singlePromptContainer.style.display = 'none';
        const backdrop = document.getElementById('ai-backdrop');
        if (backdrop) backdrop.style.display = 'none';
      });
      document.getElementById('singlePromptEmojiButton').addEventListener('click', () => {
        showPopup('Привет! Введите любой запрос и нажмите Enter.');
      });
      document.getElementById('copyResponseButton').addEventListener('click', copyResponseToClipboard);

      inputEl.focus();
    }


    window.addEventListener('keydown', function (e) {
      const multiContainer = document.getElementById('multi-selection-container');

      if (multiContainer && window.getComputedStyle(multiContainer).display !== 'none') {
        if (e.key === 'Backspace') {
          if (document.activeElement.tagName !== 'INPUT' && document.activeElement.tagName !== 'TEXTAREA') {
            e.stopImmediatePropagation();
            e.stopPropagation();
            e.preventDefault();
            console.log('Backspace нажат, закрываем мультивыбор принудительно');

            multiContainer.style.display = 'none';
            document.querySelectorAll('.multi-selected').forEach(elem => {
              elem.classList.remove('multi-selected');
              const overlay = elem.querySelector('.multi-select-overlay');
              if (overlay) overlay.remove();
            });

            multiSelectedMessages = [];
            const input = document.getElementById('multiPromptInput');
            if (input) input.value = '';
            showPopup('Мультивыбор отменён');

            setTimeout(() => {
              if (currentInputField) currentInputField.focus();
            }, 50);
          }
        }
      }
    }, true);

    // Отправка запроса
    async function sendSinglePrompt() {
      const inputEl = document.getElementById('singlePromptInput');
      const resultEl = document.getElementById('singlePromptResult');
      const copyBtn = document.getElementById('copyResponseButton');
      const userText = inputEl.value.trim();
      const selectedModel = localStorage.getItem('selectedAskChatModel')
                          || (localStorage.getItem('selectedModel') || 'gpt-4.1-mini');

      if (!userText) {
        showPopup('Введите текст запроса!');
        return;
      }

      // Сохраняем запрос в localStorage
      localStorage.setItem('lastAskAIRequest', userText);

      resultEl.style.display = 'block';
      resultEl.innerHTML = "Запрашиваем ответ...";
      copyBtn.style.display = 'block';

      // Scroll to bottom of textarea
      inputEl.scrollTop = inputEl.scrollHeight;

      // Show persistent popup with generating message
      const popupEl = document.getElementById("custom-popup");
      if (popupEl) {
        popupEl.textContent = "Генерация ответа...";
        popupEl.style.opacity = "1";
      } else {
        showPopup("Генерация ответа...", true); // Create persistent popup if doesn't exist
      }

      try {
        const systemPrompt = localStorage.getItem('systemPrompt') || DEFAULT_SYSTEM_PROMPT;
        const API_URL = "https://api.openai.com/v1/chat/completions";
        const apiToken = "********************************************************************************************************************************************************************";

        // Check if we have cached chat messages to include
        let finalUserContent = userText;
        const cachedMessages = localStorage.getItem('cachedChatMessages');

        if (cachedMessages) {
          // Include the cached messages with the user's prompt
          finalUserContent = `${userText}\n\n--- Chat Content ---\n${cachedMessages}`;
          logger.info('[AI DEBUG] Including cached chat messages in the API request');
          // Clear the cached messages after using them
          localStorage.removeItem('cachedChatMessages');
        }

        const payload = {
          model: selectedModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: finalUserContent }
          ]
        };

        const response = await fetch(API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + apiToken
          },
          body: JSON.stringify(payload)
        });
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Ошибка API: ${response.status}. ${errorText}`);
        }
        const data = await response.json();
        if (data.choices && data.choices[0] && data.choices[0].message) {
          const answer = data.choices[0].message.content.trim();
          resultEl.innerHTML = simpleMarkdownToHTML(answer);

          // Scroll the result element to the bottom
          setTimeout(() => {
            resultEl.scrollTop = resultEl.scrollHeight;
          }, 0);

          // Ensure the popup is hidden on success
          if (popupEl) {
            popupEl.style.opacity = "0";
          }
        } else {
          throw new Error('Неверный формат ответа от API');
        }
      } catch (err) {
        logger.error('Ошибка singlePrompt:', err);
        resultEl.textContent = 'Ошибка: ' + err.message;
        copyBtn.style.display = 'none';

        // Ensure popup is hidden and show error
        if (popupEl) {
          popupEl.style.opacity = "0";
        }
        showPopup('Ошибка: ' + err.message);

        // Hide backdrop on error
        const backdrop = document.getElementById('ai-backdrop');
        if (backdrop) backdrop.style.display = 'none';
      }
    }

    // Копирование ответа
    function copyResponseToClipboard() {
      const resultEl = document.getElementById('singlePromptResult');
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = resultEl.innerHTML; // Keep original HTML for copying rich text if possible, otherwise plain text

      // Attempt to copy as rich text first
      let textToCopy = tempDiv.innerHTML;
      let plainText = tempDiv.textContent || tempDiv.innerText || "";

      navigator.clipboard.writeText(plainText) // Fallback to plain text for wider compatibility
        .then(() => {
          const copyBtn = document.getElementById('copyResponseButton');
          const originalText = copyBtn.textContent;
          copyBtn.textContent = '✓ Скопировано';
          copyBtn.style.backgroundColor = '#28a745'; // Green for success
          copyBtn.style.color = '#fff';
          setTimeout(() => {
            copyBtn.textContent = originalText;
            copyBtn.style.backgroundColor = '#383838';
            copyBtn.style.color = '#bbb';
          }, 2000);
        })
        .catch(err => {
          console.error('Ошибка при копировании текста: ', err);
          showPopup('Не удалось скопировать текст');
        });
    }

    // Простейший конвертер Markdown -> HTML
    function simpleMarkdownToHTML(markdownText) {
      let htmlText = markdownText;

      // Блок кода
      htmlText = htmlText.replace(/```([a-zA-Z0-9_-]*)\n([\s\S]*?)```/g, function(match, language, code) {
        const languageClass = language ? ` class="language-${language}"` : '';
        // Added styling for pre and code to match the dark theme
        return `<pre style="background-color: #1e1e1e; color: #d4d4d4; padding: 1em; border-radius: 8px; overflow-x: auto;"><code${languageClass}>${code.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>`;
      });
      // Заголовки
      htmlText = htmlText.replace(/^###### (.*)$/gm, '<h6 style="font-weight: bold; color: #eee; margin-top: 0.8em; margin-bottom: 0.4em;">$1</h6>');
      htmlText = htmlText.replace(/^##### (.*)$/gm, '<h5 style="font-weight: bold; color: #eee; margin-top: 0.8em; margin-bottom: 0.4em;">$1</h5>');
      htmlText = htmlText.replace(/^#### (.*)$/gm, '<h4 style="font-weight: bold; color: #eee; margin-top: 1em; margin-bottom: 0.5em;">$1</h4>');
      htmlText = htmlText.replace(/^### (.*)$/gm, '<h3 style="font-weight: bold; color: #eee; margin-top: 1.2em; margin-bottom: 0.6em;">$1</h3>');
      htmlText = htmlText.replace(/^## (.*)$/gm, '<h2 style="font-weight: bold; color: #eee; margin-top: 1.5em; margin-bottom: 0.8em; border-bottom: 1px solid #444; padding-bottom: 0.3em;">$1</h2>');
      htmlText = htmlText.replace(/^# (.*)$/gm, '<h1 style="font-weight: bold; color: #eee; margin-top: 1.8em; margin-bottom: 1em; border-bottom: 1px solid #555; padding-bottom: 0.5em;">$1</h1>');

      // Горизонтальная линия
      htmlText = htmlText.replace(/^---+$/gm, '<hr style="border: none; border-top: 1px solid #444; margin: 1em 0;">');

      // Списки
      htmlText = htmlText.replace(/^(\s*)-\s+(.*)$/gm, (match, spaces, content) => {
        return `${spaces}<li style="margin-bottom: 0.3em;">${content}</li>`;
      });
      htmlText = htmlText.replace(/^(\s*)\d+\.\s+(.*)$/gm, (match, spaces, content) => {
        return `${spaces}<li>${content}</li>`;
      });
      htmlText = htmlText.replace(/(<li>.*<\/li>)/gms, '<ul>$1</ul>');

      // Таблицы
      htmlText = htmlText.replace(/^\|(.*)\|$/gm, (match, content) => {
        const cells = content.split('|').map(cell => cell.trim());
        return `<tr>${cells.map(cell => `<td>${cell}</td>`).join('')}</tr>`;
      });
      htmlText = htmlText.replace(/(<tr>.*<\/tr>)/gms, '<table>$1</table>');

      // Ссылки, картинки, цитаты
      htmlText = htmlText.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
      htmlText = htmlText.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1">');
      htmlText = htmlText.replace(/^>\s+(.*)$/gm, '<blockquote>$1</blockquote>');

      // Жирный, курсив, зачеркнутый, инлайн-код
      htmlText = htmlText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      htmlText = htmlText.replace(/__(.*?)__/g, '<strong>$1</strong>');
      htmlText = htmlText.replace(/\b_([^_]+)_\b/g, '<em>$1</em>');
      htmlText = htmlText.replace(/\*([^*\n]+)\*/g, '<em>$1</em>');
      htmlText = htmlText.replace(/~~(.*?)~~/g, '<del>$1</del>');
      htmlText = htmlText.replace(/`([^`]+)`/g, '<code>$1</code>');

      // Абзацы
      htmlText = htmlText.replace(
        /^(?!<(h[1-6]|pre|ul|ol|blockquote|table|tr|li|hr)>)(.+)$/gm,
        '<p>$2</p>'
      );
      htmlText = htmlText.replace(/<p>\s*<\/p>/g, '');

      return htmlText;
    }

    // Функция, которая делает элемент перетаскиваемым,
    // но не даёт перетаскивать при клике внутри textarea и #singlePromptResult
    function makeDraggable(container) {
      let isDragging = false;
      let offsetX = 0;
      let offsetY = 0;

      container.addEventListener('mousedown', (e) => {
        // Если клик внутри textarea (поле ввода) или внутри блока ответа — не тащим
        if (
          e.target.closest('#singlePromptInput') ||
          e.target.closest('#singlePromptResult')
        ) {
          return;
        }
        isDragging = true;
        const rect = container.getBoundingClientRect();
        offsetX = e.clientX - rect.left;
        offsetY = e.clientY - rect.top;
        container.style.cursor = 'grabbing';
      });

      document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        e.preventDefault();
        container.style.left = (e.clientX - offsetX) + 'px';
        container.style.top = (e.clientY - offsetY) + 'px';
        // Сброс bottom, чтобы не конфликтовало с top
        container.style.bottom = 'auto';
        // Убираем transform, чтобы не было сдвигов
        container.style.transform = 'none';
      });

      document.addEventListener('mouseup', () => {
        isDragging = false;
        container.style.cursor = 'default';
      });
    }





    // Флаг записи, объект MediaRecorder и массив для аудио-чанков
  let isRecording = false;
  let mediaRecorder;
  let recordedChunks = [];

  // Функция создания кнопки микрофона
  function createMicrophoneButton() {
    const button = document.createElement("button");
    button.id = "microphoneButton";
    button.innerText = "";
    // Стили: фиксированное позиционирование в левом нижнем углу
    button.style.position = "fixed";
    button.style.bottom = "20px";
    button.style.left = "500px";
    button.style.zIndex = "11000";
    button.style.padding = "10px";
    button.style.fontSize = "24px";
    button.style.borderRadius = "50%";
    button.style.border = "none";
    button.style.color = "#fff";
    button.style.cursor = "pointer";
    document.body.appendChild(button);

    button.addEventListener("click", toggleRecording);
  }

  async function toggleRecording() {
    if (!isRecording) {
      await startRecording();
    } else {
      stopRecording();
    }
  }

  async function startRecording() {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      recordedChunks = [];
      mediaRecorder = new MediaRecorder(stream);
      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          recordedChunks.push(e.data);
        }
      };
      mediaRecorder.onstop = async () => {
        const blob = new Blob(recordedChunks, { type: "audio/wav" });
        try {
          const transcript = await transcribeAudio(blob);
          if (currentInputField) {
            currentInputField.innerText = transcript;
          }
          showPopup("Распознавание завершено");
        } catch (err) {
          showPopup("Ошибка транскрипции");
          console.error(err);
        }
      };
      mediaRecorder.start();
      isRecording = true;
      document.getElementById("microphoneButton").style.backgroundColor = "red";
      showPopup("Запись началась");
    } catch (err) {
      showPopup("Ошибка доступа к микрофону");
      console.error(err);
    }
  }

  function stopRecording() {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
      isRecording = false;
      document.getElementById("microphoneButton").style.backgroundColor = "transparent";
      showPopup("Запись остановлена");
    }
  }

  async function transcribeAudio(blob) {
    const API_URL = "https://api.openai.com/v1/audio/transcriptions";
    const apiToken = "********************************************************************************************************************************************************************";
    const formData = new FormData();
    formData.append("file", blob, "audio.wav");
    formData.append("model", "whisper-1");
    const response = await fetch(API_URL, {
      method: "POST",
      headers: {
        "Authorization": "Bearer " + apiToken
      },
      body: formData
    });
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error("Ошибка API транскрипции: " + errorText);
    }
    const data = await response.json();
    return data.text;
  }

  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", createMicrophoneButton);
  } else {
    createMicrophoneButton();
  }

    const quickMessagesStyle = document.createElement('style');
    quickMessagesStyle.innerHTML = `
      .quick-messages-popup-item.focused {
        background-color: #2e2e2e;
        color: white;
      }
    `;
    document.head.appendChild(quickMessagesStyle);


    chatNoteDiv.addEventListener("keydown", (e) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();

        const selection = window.getSelection();
        const range = selection.getRangeAt(0);
        const br = document.createElement('br');
        range.deleteContents();
        range.insertNode(br);

        range.setStartAfter(br);
        range.setEndAfter(br);
        selection.removeAllRanges();
        selection.addRange(range);

        // Оповещаем об изменениях для сохранения
        chatNoteDiv.dispatchEvent(new Event('input', { bubbles: true }));
        return false;
      }

      // Предотвращаем обработку стрелок Telegram'ом, когда находимся в заметках
      if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key)) {
        e.stopPropagation();
      }
    });

  function cleanupExistingSummaryElements() {
      document.querySelectorAll('.chat-summary-container, .summary-button, #simple-summary-panel, #summary-floating-button, #summary-panel, #summary-button, .summary-resize-handle').forEach(el => {
        el.remove();
      });
    }

    cleanupExistingSummaryElements();

    const summaryStyles = document.createElement('style');
    summaryStyles.textContent = `
      #summary-panel {
        display: none !important;
      }

      #summary-button {
        display: none !important;
      }
    `;
    document.head.appendChild(summaryStyles);

    initializeGroupInfoPanel();

    /* Закомментировано и деактивировано плавающее окно саммари
    createChatSummary(); */

    // Предотвращаем возможное создание плавающего окна саммари
    window.createChatSummary = function() {
      console.log("Создание плавающего окна саммари отключено");
      return false;
    };

    // Регулярно удаляем любые появляющиеся плавающие панели саммари
    setInterval(cleanupExistingSummaryElements, 5000);

    // Add styles for the summary tab button
    const summaryTabButtonStyle = document.createElement('style');
    summaryTabButtonStyle.textContent = `
      #summary-panel.minimized {
        height: auto !important;
      }

      #summary-tab-button {
        background-color: transparent;
        border: none;
        color: white;
        cursor: pointer;
        outline: none;
        font-size: 18px;
        transition: transform 0.2s ease;
      }

      #summary-tab-button:hover {
        transform: scale(1.1);
      }
    `;
    document.head.appendChild(summaryTabButtonStyle);

    // Group Info popup integration
    function initializeGroupInfoPanel() {
      // Watch for Group Info panel elements
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.addedNodes && mutation.addedNodes.length > 0) {
            for (let i = 0; i < mutation.addedNodes.length; i++) {
              const node = mutation.addedNodes[i];
              if (node.nodeType === Node.ELEMENT_NODE) {
                // Check if this is the Group Info panel
                if (node.querySelector && (
                    node.querySelector('.Group') ||
                    node.textContent.includes('Group Info') ||
                    node.querySelector('[aria-label="Group Info"]')
                  )) {

                  setTimeout(() => {
                    addGroupInfoButtons(node);
                  }, 500);
                }
              }
            }
          }
        });
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    function addGroupInfoButtons(panelNode) {
      // Check if we already added the buttons
      if (panelNode.querySelector('.telegram-logger-summary-btn')) {
          return;
        }

      // Create the container div for the buttons if needed
      let container = panelNode.querySelector('.telegram-logger-buttons');
      if (!container) {
        container = document.createElement('div');
        container.style.padding = '10px';
    container.style.display = 'flex';
    container.style.flexDirection = 'column';
        container.style.gap = '10px';
        container.className = 'telegram-logger-buttons';

        // Find a good place to insert our container
        const possibleContainers = panelNode.querySelectorAll('div');
        let inserted = false;

        for (const div of possibleContainers) {
          if (div.childElementCount > 0 && div.getBoundingClientRect().height > 100) {
            div.appendChild(container);
            inserted = true;
            break;
          }
        }

        if (!inserted) {
          // If we couldn't find a good place, just append to the panel
          panelNode.appendChild(container);
        }
      }

      // Add summary section
      const summarySection = document.createElement('div');
      summarySection.style.backgroundColor = '#2e2e2e';
      summarySection.style.color = '#fff';
      summarySection.style.borderRadius = '8px';
      summarySection.style.padding = '15px';
      summarySection.style.marginBottom = '10px';
      summarySection.style.border = '1px solid #444';
      summarySection.id = 'summary-content-section';
      summarySection.innerHTML = '<div style="text-align: center; font-weight: bold; font-size: 16px;">Summary</div><div id="summary-content-text">Click "Load Summary" to view chat summary</div>';
      container.appendChild(summarySection);

      // Add message count display
      const messageCountDiv = document.createElement('div');
      messageCountDiv.style.backgroundColor = '#2e2e2e';
      messageCountDiv.style.color = '#fff';
      messageCountDiv.style.borderRadius = '8px';
      messageCountDiv.style.padding = '15px';
      messageCountDiv.style.marginBottom = '10px';
      messageCountDiv.style.border = '1px solid #444';
      messageCountDiv.id = 'message-count-display';

      const messagesCount = collectMessages().length;
      messageCountDiv.innerHTML = `<div style="text-align: center; font-weight: bold; font-size: 16px;">Count of messages cached for this chat</div><div id="message-count-value" style="font-size: 16px;">${messagesCount} messages</div>`;
      container.appendChild(messageCountDiv);

      // Add Ask AI button
      const askAIButton = document.createElement('button');
      askAIButton.textContent = 'Ask AI за весь чат';
      askAIButton.className = 'telegram-logger-askAI-btn';
      askAIButton.style.backgroundColor = '#6d739e';
      askAIButton.style.color = 'white';
      askAIButton.style.border = 'none';
      askAIButton.style.borderRadius = '8px';
      askAIButton.style.padding = '12px';
      askAIButton.style.cursor = 'pointer';
      askAIButton.style.width = '100%';
      askAIButton.style.fontSize = '16px';

      askAIButton.addEventListener('click', () => {
        // Get the current chat ID
        const chatId = getCurrentChatId();
        const currentChatMessages = chatMessages[chatId];

        if (!currentChatMessages || currentChatMessages.size === 0) {
          showPopup("No messages found in this chat");
          return;
        }

        try {
          // Сначала соберем все сообщения в формат, удобный для отправки
          const allMessages = Array.from(currentChatMessages.values())
            .sort((a, b) => a.mid - b.mid) // Сортируем по ID сообщения, чтобы сохранить порядок
            .map(msg => `${getSenderName(msg)}: ${processMessageText(msg)}`)
            .join('\n\n');

          logger.info(`[AI DEBUG] Group Info: Prepared ${currentChatMessages.size} messages for AI analysis`);

          // Set prompt text for the user interface
          const promptText = "Analyze the contents of this chat and provide insights";

          // Store the messages in localStorage for the API call to use
          localStorage.setItem('cachedChatMessages', allMessages);

          // Open the single prompt UI with just the prompt text
          if (!singlePromptContainer) {
            openSinglePromptUI(promptText, false);
          } else {
            // Панель уже существует, показываем ее и заполняем
            singlePromptContainer.style.display = 'flex';
            const inputEl = document.getElementById('singlePromptInput');
            if (inputEl) {
              inputEl.value = promptText;
              inputEl.focus();
              // Auto-scroll to the bottom after content is loaded
              inputEl.scrollTop = inputEl.scrollHeight;
            }
          }
        } catch (error) {
          console.error("Error opening AI prompt:", error);
          showPopup("Error preparing chat messages: " + error.message);
        }
      });

      container.appendChild(askAIButton);

      // Add "Load Summary" button
      const loadSummaryButton = document.createElement('button');
      loadSummaryButton.textContent = 'Load Summary';
      loadSummaryButton.className = 'telegram-logger-summary-btn';
      loadSummaryButton.style.backgroundColor = '#6d739e';
      loadSummaryButton.style.color = 'white';
      loadSummaryButton.style.border = 'none';
      loadSummaryButton.style.borderRadius = '8px';
      loadSummaryButton.style.padding = '12px';
      loadSummaryButton.style.cursor = 'pointer';
      loadSummaryButton.style.width = '100%';
      loadSummaryButton.style.fontSize = '16px';
      loadSummaryButton.style.marginBottom = '10px';

      loadSummaryButton.addEventListener('click', async () => {
        const summaryContentText = document.getElementById('summary-content-text');
        if (summaryContentText) {
          summaryContentText.textContent = 'Loading summary...';

          if (summaryUpdateInProgress) {
            summaryContentText.textContent = 'Summary update already in progress...';
            return;
          }

          const chatId = getCurrentChatId();
          let summary = loadSummary(chatId);

          if (!summary) {
            const messages = collectMessages();
            showPopup('Generating summary...', true);
            summary = await generateSummaryWithAPI(messages);
            if (summary) {
              saveSummary(chatId, summary);
            } else {
              summary = createDefaultSummary();
            }
            hidePopup();
          }

          if (summary) {
            let summaryHtml = `<p style="font-weight: bold; color: #fff; margin-bottom: 10px;">${summary.shortSummary}</p><ul style="padding-left: 20px; margin-top: 8px; list-style-type: disc;">`;
            summary.fullSummary.forEach(point => {
              summaryHtml += `<li style="margin-bottom: 8px; line-height: 1.4;">${point}</li>`;
            });
            summaryHtml += '</ul>';
            summaryContentText.innerHTML = summaryHtml;
    } else {
            summaryContentText.textContent = 'Failed to load summary.';
          }
        }
      });

      // Insert Load Summary button at the top
      container.insertBefore(loadSummaryButton, container.firstChild);
    }

    initializeGroupInfoPanel();

    /* Uncomment the following line if you need the original summary panel functionality
    createChatSummary(); */

    // Add event listener for the Load Chat Summary button in the Logger panel
    const loadLoggerSummaryBtn = document.getElementById('loadLoggerSummaryBtn');
    if (loadLoggerSummaryBtn) {
      loadLoggerSummaryBtn.addEventListener('click', async () => {
        const summaryContentText = document.getElementById('logger-summary-content-text');
        if (summaryContentText) {
          summaryContentText.textContent = 'Loading summary...';

          if (summaryUpdateInProgress) {
            summaryContentText.textContent = 'Summary update already in progress...';
            return;
          }

          const chatId = window.location.hash.substring(1) || "default";

          // Always collect current messages to refresh the summary
          const messagesArray = [];
          if (chatMessages[chatId]) {
            const messagesMap = chatMessages[chatId];
            Array.from(messagesMap.values()).forEach(msg => {
              messagesArray.push(`${getSenderName(msg)}: ${processMessageText(msg)}`);
            });
          }

          if (messagesArray.length === 0) {
            summaryContentText.textContent = 'No messages found to generate summary.';
            return;
          }

          // Removed logic for loading summary from cache and checking message count
          // Now, always generate a new summary

          showPopup('Generating summary...', true);
          let summary = await generateSummaryWithAPI(messagesArray);
          hidePopup();

          if (summary && summary.shortSummary && summary.fullSummary) { // Added null check for summary properties
            // Add message count to the summary object (can be used for display or debugging if needed)
            summary.messageCount = messagesArray.length;
            // saveSummary(chatId, summary); // Saving is now disabled, no need to call this

            let summaryHtml = `<p style="font-weight: bold; color: #fff; margin-bottom: 10px;">${summary.shortSummary}</p><ul style="padding-left: 20px; margin-top: 8px; list-style-type: disc;">`;
            summary.fullSummary.forEach(point => {
              summaryHtml += `<li style="margin-bottom: 8px; line-height: 1.4;">${point}</li>`;
            });
            summaryHtml += '</ul>';
            summaryContentText.innerHTML = summaryHtml;
          } else {
            // If summary generation failed or returned unexpected structure, show default or error
            const defaultSummary = createDefaultSummary();
            summaryContentText.textContent = 'Failed to load summary. Displaying default info.'; // More informative message
            let summaryHtml = `<p style="font-weight: bold; color: #fff; margin-bottom: 10px;">${defaultSummary.shortSummary}</p><ul style="padding-left: 20px; margin-top: 8px; list-style-type: disc;">`;
            defaultSummary.fullSummary.forEach(point => {
              summaryHtml += `<li style="margin-bottom: 8px; line-height: 1.4;">${point}</li>`;
            });
            summaryHtml += '</ul>';
            summaryContentText.innerHTML = summaryHtml;
          }
        }
      });
    }

    // Add event listener for the Ask AI button in the Logger panel
    const loggerAskAiBtn = document.getElementById('loggerAskAiBtn');
    if (loggerAskAiBtn) {
      loggerAskAiBtn.addEventListener('click', () => {
        logger.info('[AI DEBUG] loggerAskAiBtn clicked');

        // Get the current chat ID and check for messages
        const chatId = window.location.hash.substring(1) || "default";
        const currentChatMessages = chatMessages[chatId];

        if (!currentChatMessages || currentChatMessages.size === 0) {
          showPopup("No messages found in this chat");
          return;
        }

        try {
          // Сначала соберем все сообщения в формат, удобный для отправки
          const allMessages = Array.from(currentChatMessages.values())
            .sort((a, b) => a.mid - b.mid) // Сортируем по ID сообщения, чтобы сохранить порядок
            .map(msg => `${getSenderName(msg)}: ${processMessageText(msg)}`)
            .join('\n\n');

          logger.info(`[AI DEBUG] Group Info: Prepared ${currentChatMessages.size} messages for AI analysis`);

          // Вызываем UI с готовым текстом сообщений
          const promptText = "Analyze the full chat content and provide insights based on all loaded messages.";
          const backdrop = document.getElementById('ai-backdrop'); // Get backdrop reference

          // Создаем панель запроса
          if (!singlePromptContainer) {
            openSinglePromptUI(promptText, false); // Создаем панель без автоматической загрузки

            // Теперь вручную заполняем текстовое поле всеми сообщениями
            setTimeout(() => {
              const inputEl = document.getElementById('singlePromptInput');
              if (inputEl) {
                inputEl.value = promptText + "\n\n--- Chat Content ---\n" + allMessages;
                inputEl.focus();
                // Auto-scroll to the bottom after content is loaded
                inputEl.scrollTop = inputEl.scrollHeight;
              }
            }, 100);
          } else {
            // Панель уже существует, показываем ее и заполняем
            // *** FIX: Explicitly show backdrop when reusing panel ***
            if (backdrop) backdrop.style.display = 'block';
            singlePromptContainer.style.display = 'flex';
            const inputEl = document.getElementById('singlePromptInput');
            if (inputEl) {
              inputEl.value = promptText + "\n\n--- Chat Content ---\n" + allMessages;
              inputEl.focus();
              // Auto-scroll to the bottom after content is loaded
              setTimeout(() => {
                inputEl.scrollTop = inputEl.scrollHeight;
              }, 0);
            }
          }
        } catch (error) {
          console.error("Error opening AI prompt:", error);
          showPopup("Error preparing chat messages: " + error.message);
        }
      });
    }

    // Define any missing functions that might be needed
    if (typeof getCurrentChatId !== 'function') {
      function getCurrentChatId() {
        return window.location.hash.substring(1) || "default";
      }
    }

    // Ensure we have access to chatMessages
    if (typeof chatMessages === 'undefined') {
      console.warn("chatMessages object not found, creating empty object");
      window.chatMessages = {};
    }

    // Update message counter in the Logger panel
    function updateLoggerMessageCounter() {
      const inlineCounter = document.getElementById('message-count-inline');
      if (inlineCounter) {
        const chatId = window.location.hash.substring(1) || "default";
        const messages = chatMessages[chatId];
        const count = messages ? messages.size : 0;
        inlineCounter.textContent = count;
      }
    }

    // Call updateLoggerMessageCounter immediately and periodically
    updateLoggerMessageCounter();
    setInterval(updateLoggerMessageCounter, 5000);

    // Функция для создания дефолтного саммари
    function createDefaultSummary() {
      return {
        shortSummary: "Демонстрационное саммари чата",
        fullSummary: [
          "Это временное демонстрационное саммари",
          "Для реального саммари требуется настроить API ключ OpenAI",
          "Проверьте, что в localStorage установлен ключ openaiToken",
          "Или обратитесь к разработчику для помощи с интеграцией API",
          "Вы можете самостоятельно проанализировать сообщения, используя кнопку 'Ask AI за весь чат'",
          "В чате найдено " + (collectMessages().length) + " сообщений"
        ]
      };
    }

    // Глобальная переменная для отслеживания статуса обновления саммари
    let summaryUpdateInProgress = false;

    // Функция для генерации саммари через API
    async function generateSummaryWithAPI(messages) {
      if (summaryUpdateInProgress) {
        console.log("Обновление саммари уже выполняется...");
        return null;
      }

      summaryUpdateInProgress = true;
      console.log("Генерация саммари через API для", messages.length, "сообщений");

      try {
        // IMPORTANT: Используем ВСЕ загруженные сообщения вместо прежнего ограничения в 100
        // This ensures that if 200 messages are loaded, all 200 will be sent for summary generation
        const messagesText = messages.join("\n\n");

        const DEFAULT_SUMMARY_PROMPT = `Создай краткое саммари (до 50 слов) и список из 5-10 основных пунктов, о чем идет речь в этой беседе. Выделяй главные мысли, стиль общения в чате. Внимательно замечай детали, бери факты из чата. Используй формат JSON:
  {
    "shortSummary": "Краткое описание...",
    "fullSummary": ["Пункт 1", "Пункт 2", ...]
  }`;

        let summaryPromptTemplate = localStorage.getItem("summaryPrompt") || DEFAULT_SUMMARY_PROMPT;
        console.log("Использую промпт для саммари:", summaryPromptTemplate.substring(0, 50) + "...");

        const summaryPrompt = `${summaryPromptTemplate}

  Сообщения чата:
  ${messagesText}`;

        const API_URL = "https://api.openai.com/v1/chat/completions";
        const apiToken = "********************************************************************************************************************************************************************";


        const selectedModel = localStorage.getItem("selectedSummaryModel") || "gpt-4.1-mini";
        console.log("Using model for summary:", selectedModel);

        const payload = {
          model: selectedModel,
          messages: [
            { role: 'system', content: "Ты эксперт по summarise чатов, который понятно, точно, удобно для чтения и не теряя важных фактов делает саммари. Ответ должен быть только в JSON формате." },
            { role: 'user', content: summaryPrompt }
          ]
        };

        console.log("Отправка запроса к API с", messages.length, "сообщениями...");
        const response = await fetch(API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + apiToken
          },
          body: JSON.stringify(payload)
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Ошибка API: ${response.status}. ${errorText}`);
        }

        const data = await response.json();
        console.log("Получен ответ от API:", data);

        if (data.choices && data.choices[0] && data.choices[0].message) {
          const content = data.choices[0].message.content.trim();

          try {
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            const jsonString = jsonMatch ? jsonMatch[0] : content;
            const result = JSON.parse(jsonString);

            summaryUpdateInProgress = false;
            return result;
          } catch (jsonError) {
            console.error("Ошибка при парсинге JSON из ответа API:", jsonError);

            summaryUpdateInProgress = false;
            return {
              shortSummary: "Саммари чата",
              fullSummary: [content]
            };
          }
        } else {
          throw new Error('Неверный формат ответа от API');
        }
      } catch (error) {
        console.error("Ошибка при создании саммари через API:", error);

        // Добавляем детальную информацию об ошибке
        let errorDetail = error.message || "Неизвестная ошибка";

        // Если ошибка связана с API ключом
        if (errorDetail.includes("API key") ||
            errorDetail.includes("authentication") ||
            errorDetail.includes("token") ||
            errorDetail.includes("401")) {
          errorDetail = "Проблема с API ключом OpenAI. Ключ может быть недействительным или просрочен.";
        }

        // Если ошибка связана с форматом данных
        if (errorDetail.includes("format") ||
            errorDetail.includes("parse") ||
            errorDetail.includes("JSON")) {
          errorDetail = "Проблема с форматом данных при обмене с API.";
        }

        console.log("Дополнительная информация об ошибке:", errorDetail);

        summaryUpdateInProgress = false;
        return {
          shortSummary: "Саммари недоступно",
          fullSummary: [
            "Не удалось сгенерировать саммари чата",
            `Ошибка: ${errorDetail}`,
            "Попробуйте позже или проверьте консоль браузера (F12) для деталей"
          ]
        };
      }
    }

    // Функция для сохранения саммари в localStorage
    function saveSummary(chatId, summary) {
      try {
        const key = `chat_summary_${chatId}`;
        // localStorage.setItem(key, JSON.stringify(summary)); // Disabled saving
        console.log(`Summary saving disabled for chat ${chatId}. Would have saved:`, summary);
        return true;
      } catch (error) {
        console.error("Error saving summary (though saving is disabled):", error);
        return false;
      }
    }

    // Функция для загрузки саммари из localStorage
    function loadSummary(chatId) {
      try {
        // const key = `chat_summary_${chatId}`;
        // const data = localStorage.getItem(key);
        // if (!data) return null;

        // const summary = JSON.parse(data);
        // console.log(`Summary loaded for chat ${chatId}`);
        // return summary;
        console.log(`Summary loading disabled for chat ${chatId}. Returning null.`);
        return null; // Always return null to force regeneration
      } catch (error) {
        console.error("Error loading summary (though loading is disabled):", error);
        return null;
      }
    }

    // Функция для сбора сообщений из текущего чата
    function collectMessages() {
      const chatId = window.location.hash.substring(1) || "default";
      const messagesArray = [];

      if (chatMessages[chatId]) {
        const messagesMap = chatMessages[chatId];
        Array.from(messagesMap.values()).forEach(msg => {
          if (msg.text) {
            messagesArray.push(`${getSenderName(msg)}: ${processMessageText(msg)}`);
          }
        });
      }

      console.log(`Collected ${messagesArray.length} messages from chat ${chatId}`);
      return messagesArray;
    }

    // Add event listener for ESC key to close AI prompt
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        // Hide the AI prompt if it's visible
        if (singlePromptContainer && singlePromptContainer.style.display !== 'none') {
          // *** FIX: Prevent Telegram shortcut and stop propagation ***
          e.preventDefault();
          e.stopPropagation();
          logger.info('[AI DEBUG] Escape key pressed, hiding backdrop and container.');
          singlePromptContainer.style.display = 'none';
          const backdrop = document.getElementById('ai-backdrop');
          if (backdrop) backdrop.style.display = 'none';
        }
      }
    }, true); // Use capture phase


    function isShortcutCaptureMode() {
      return document.activeElement &&
            document.activeElement.id &&
            document.activeElement.id.startsWith('shortcut-');
    }

    // Function to check if a keyboard event matches a shortcut string (e.g. "Command+;")
    function checkShortcutMatch(e, shortcutStr) {
      if (!shortcutStr) return false;

      const parts = shortcutStr.split('+');
      const key = parts[parts.length - 1].toLowerCase();

      // Check modifiers
      const hasCommand = parts.some(p => p.toLowerCase() === 'command' || p.toLowerCase() === 'cmd');
      const hasControl = parts.some(p => p.toLowerCase() === 'control' || p.toLowerCase() === 'ctrl');
      const hasAlt = parts.some(p => p.toLowerCase() === 'alt' || p.toLowerCase() === 'option');
      const hasShift = parts.some(p => p.toLowerCase() === 'shift');

      // Check if modifiers match
      if (hasCommand && !e.metaKey) return false;
      if (hasControl && !e.ctrlKey) return false;
      if (hasAlt && !e.altKey) return false;
      if (hasShift && !e.shiftKey) return false;

      // Check the main key
      // Handle semicolon specially
      if (key === ';' && e.key === ';') return true;

      // For other keys, do a case-insensitive comparison
      return e.key.toLowerCase() === key.toLowerCase();
    }

    // Добавляем перед function clearMultiSelection() функцию для отображения меню избранных чатов
    function showFavoriteChatsMenu(event, msgElem) {
      // Удаляем предыдущее меню, если оно есть
      const existingMenu = document.getElementById('favorite-chats-menu');
      if (existingMenu) existingMenu.remove();

      // Получаем список избранных чатов из localStorage
      const favoriteChats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');

      // Если список пуст, предлагаем добавить избранные чаты
      if (favoriteChats.length === 0) {
        // Создаем новый список
        const defaultChats = [
          { id: '1', name: 'Избранный чат 1', order: 0 },
          { id: '2', name: 'Избранный чат 2', order: 1 }
        ];
        localStorage.setItem('favorite_chats', JSON.stringify(defaultChats));
        showPopup('Добавлены демо-чаты в избранное. Пожалуйста, отредактируйте их.');
      }

      const updatedFavoriteChats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');

      updatedFavoriteChats.forEach((chat, index) => {
        if (chat.order === undefined) {
          chat.order = index * 10;
        }
      });

      // Сортировка только по порядковому номеру, без приоритизации чатов с префиксом "Клиент"
      updatedFavoriteChats.sort((a, b) => {
        return a.order - b.order;
      });

      localStorage.setItem('favorite_chats', JSON.stringify(updatedFavoriteChats));

      const menu = document.createElement('div');
      menu.id = 'favorite-chats-menu';
      menu.style.position = 'fixed';
      menu.style.backgroundColor = 'rgb(21, 24, 27)';
      menu.style.border = 'none';
      menu.style.opacity = '99%';
      menu.style.borderRadius = '10px';
      menu.style.padding = '8px 0';
      menu.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.4)';
      menu.style.zIndex = '99999'; // Увеличиваем z-index для гарантии отображения
      menu.style.minWidth = '220px';
      menu.style.color = 'white';
      menu.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';
      menu.style.fontSize = '14px';

      const header = document.createElement('div');
      header.innerText = 'Избранные чаты';
      header.style.padding = '8px 16px';
      header.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
      header.style.marginBottom = '4px';
      header.style.fontWeight = 'bold';
      header.style.color = 'rgba(255, 255, 255, 0.9)';
      menu.appendChild(header);

      updatedFavoriteChats.forEach(chat => {
        const item = document.createElement('div');
        item.className = 'favorite-chat-item';
        item.innerText = chat.name;
        item.style.padding = '12px 16px';
        item.style.cursor = 'pointer';
        item.style.color = 'rgba(255, 255, 255, 0.9)';
        item.style.transition = 'background-color 0.15s';
        item.style.display = 'flex';
        item.style.alignItems = 'center';

        // Удаляем специальное оформление для чатов с названием "Клиент"

        const icon = document.createElement('span');
        icon.innerHTML = '&#10150;';
        icon.style.marginRight = '15px';
        icon.style.fontSize = '16px';
        item.prepend(icon);

        item.addEventListener('mouseover', () => {
          item.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        });

        item.addEventListener('mouseout', () => {
          item.style.backgroundColor = 'transparent';
        });

        item.addEventListener('click', () => {
          forwardMessageToChat(msgElem, chat);
          menu.remove();
        });

        menu.appendChild(item);
      });

      // Добавляем разделитель
      const divider = document.createElement('div');
      divider.style.height = '1px';
      divider.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
      divider.style.margin = '4px 0';
      menu.appendChild(divider);

      // Добавляем пункт "Управление"
      const manageItem = document.createElement('div');
      manageItem.className = 'favorite-chat-item';
      manageItem.style.padding = '12px 16px';
      manageItem.style.cursor = 'pointer';
      manageItem.style.color = 'rgba(255, 255, 255, 0.9)';
      manageItem.style.transition = 'background-color 0.15s';
      manageItem.style.display = 'flex';
      manageItem.style.alignItems = 'center';

      // Добавляем иконку настроек
      const settingsIcon = document.createElement('span');
      settingsIcon.innerHTML = '⚙️';
      settingsIcon.style.marginRight = '15px';
      settingsIcon.style.fontSize = '16px';

      manageItem.appendChild(settingsIcon);
      manageItem.appendChild(document.createTextNode('Управление чатами'));

      manageItem.addEventListener('mouseover', () => {
        manageItem.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
      });

      manageItem.addEventListener('mouseout', () => {
        manageItem.style.backgroundColor = 'transparent';
      });

      manageItem.addEventListener('click', () => {
        showFavoriteChatManager();
        menu.remove();
      });

      menu.appendChild(manageItem);

      document.body.appendChild(menu);

      const menuHeight = menu.offsetHeight;
      const viewportHeight = window.innerHeight;

      // Изменяем логику позиционирования меню
      let topPosition = event.clientY;
      // Если меню было вызвано кнопкой "Переслать", размещаем его выше кнопки
      if (event.target && event.target.id === 'multiPromptForward') {
        topPosition = topPosition - menuHeight - 10;
      } else {
        // Иначе проверяем, поместится ли меню ниже точки клика
        if (topPosition + menuHeight > viewportHeight - 10) {
          topPosition = topPosition - menuHeight - 10;
        } else {
          topPosition = topPosition + 10;
        }
      }

      if (topPosition < 10) {
        topPosition = 10;
      }

      const viewportWidth = window.innerWidth;
      const menuWidth = menu.offsetWidth;
      let leftPosition = event.clientX - (menuWidth / 2); // Центрируем меню по горизонтали

      if (leftPosition + menuWidth > viewportWidth - 10) {
        leftPosition = viewportWidth - menuWidth - 10;
      }

      if (leftPosition < 10) {
        leftPosition = 10;
      }

      menu.style.left = `${leftPosition}px`;
      menu.style.top = `${topPosition}px`;

      // Добавляем обработчик для закрытия меню при клике вне его
      document.addEventListener('click', function closeMenu(e) {
        if (!menu.contains(e.target) &&
            e.target !== msgElem.querySelector('.super-favorite-forward-btn') &&
            e.target.id !== 'multiPromptForward') {
          menu.remove();
          document.removeEventListener('click', closeMenu);
        }
      });
    }

    function forwardMessageToChat(msgElem, chat) {
      const hasMultipleSelected = multiSelectedMessages.length > 0;
      let messageText = '';

      if (hasMultipleSelected) {
        const selectedMessages = multiSelectedMessages.filter(item => item.chatId === chatId);

        if (selectedMessages.length > 0) {
          messageText = selectedMessages.map(item => {
            return cleanMessageText(item.text);
          }).join("\n\n");
        } else {
          messageText = msgElem.innerText.trim();
          messageText = cleanMessageText(messageText);
        }
      } else {
        messageText = msgElem.innerText.trim();
        messageText = cleanMessageText(messageText);
      }

      logger.info('Пересылка сообщения в избранный чат', {
        чат: chat.name,
        id: chat.id,
        количество_сообщений: hasMultipleSelected ? multiSelectedMessages.length : 1,
        текст: messageText.slice(0, 50) + '...'
      });

      const currentChatId = chatId || 'неизвестный';

      const forwardedText = messageText;

      if (multiSelectionContainer && multiSelectionContainer.style.display !== 'none') {
        clearMultiSelection();
      }

      localStorage.setItem('pendingForwardText', forwardedText);

      try {
        navigator.clipboard.writeText(forwardedText)
          .then(() => {
            // Переходим в выбранный чат
            if (chat.id) {
              // Открываем чат в текущей вкладке
              window.location.href = `https://web.telegram.org/k/#${chat.id}`;
              showPopup(`Переход в чат "${chat.name}", сообщение будет вставлено автоматически.`);
            } else {
              showPopup(`Сообщение скопировано в буфер обмена! Вставьте его в чат "${chat.name}"`);
            }
            logger.info('Сообщение скопировано и переход в чат', {id: chat.id, name: chat.name});
          })
          .catch(err => {
            logger.error('Ошибка при копировании в буфер обмена:', err);
            showPopup('Не удалось скопировать сообщение. Попробуйте вручную.');
          });
      } catch (e) {
        logger.error('API буфера обмена недоступен:', e);

        const tempInput = document.createElement('textarea');
        tempInput.value = forwardedText;
        tempInput.style.position = 'fixed';
        tempInput.style.left = '-9999px';
        document.body.appendChild(tempInput);
        tempInput.select();
        tempInput.setSelectionRange(0, 99999);

        try {
          const successful = document.execCommand('copy');
          if (successful) {
            if (chat.id) {
              window.location.href = `https://web.telegram.org/a/#${chat.id}`;
              showPopup(`Переход в чат "${chat.name}", сообщение будет вставлено автоматически.`);
            } else {
              showPopup(`Сообщение скопировано в буфер обмена! Вставьте его в чат "${chat.name}"`);
            }
          } else {
            throw new Error('Копирование не выполнено');
          }
        } catch (err) {
          showPopup('Не удалось автоматически скопировать.');
          showCopyTextModal(forwardedText, chat.name, chat.id);
        } finally {
          document.body.removeChild(tempInput);
        }
      }
    }

    // Функция для очистки текста сообщения от времени и специальных символов
    function cleanMessageText(text) {
      const originalText = text;

      text = text.replace(/^\s*\d{1,2}:\d{2}(:\d{2})?\s*/gm, '');

      text = text.replace(/\b\d{1,2}:\d{2}\s*(AM|PM|am|pm)\b/g, '');

      text = text.replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu, '');

      text = text.replace(/[📩✉️📝📎📌📍🔖📑🔗📤📥🖇️📂📁📃📄📊📈📉📊📋📇📅📆🗓️]/g, '');

      text = text.replace(/[☑️✓✔️✅]/g, '');

      const lines = text.split('\n');
      const cleanedLines = lines.map(line => {
        line = line.trim();

        if (/^\d+$/.test(line)) {
          return '';
        }



        line = line.replace(/\s+/g, ' ');

        return line;
      });

      // Соединяем строки, пропуская пустые
      text = cleanedLines.filter(line => line.length > 0).join('\n');

      // Убираем множественные переносы строк (более 2)
      text = text.replace(/\n{3,}/g, '\n\n');

      // Логируем изменения, если они существенны
      if (text.length < originalText.length * 0.8) {
        logger.info('Текст сообщения был значительно очищен', {
          было: originalText.length,
          стало: text.length
        });
      }

      return text.trim();
    }

    // Функция для отображения модального окна с текстом для копирования
    function showCopyTextModal(text, chatName, chatId) {
      // Создаем модальное окно
      const modal = document.createElement('div');
      modal.style.position = 'fixed';
      modal.style.top = '50%';
      modal.style.left = '50%';
      modal.style.transform = 'translate(-50%, -50%)';
      modal.style.background = '#1A1A1A';
      modal.style.padding = '20px';
      modal.style.borderRadius = '8px';
      modal.style.boxShadow = '0 0 20px rgba(0,0,0,0.5)';
      modal.style.zIndex = '10002';
      modal.style.maxWidth = '80%';
      modal.style.width = '500px';

      // Добавляем заголовок
      const header = document.createElement('h3');
      header.textContent = 'Скопируйте текст для пересылки';
      header.style.margin = '0 0 10px 0';
      header.style.color = '#fff';
      modal.appendChild(header);

      // Добавляем инструкцию
      const instructions = document.createElement('p');
      instructions.textContent = chatId ?
        `Нажмите "Перейти в чат ${chatName}", текст будет вставлен автоматически:` :
        `Скопируйте текст и вставьте его в чат "${chatName}":`;
      instructions.style.margin = '0 0 15px 0';
      instructions.style.color = '#ccc';
      modal.appendChild(instructions);

      // Добавляем текстовое поле
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.width = '100%';
      textarea.style.height = '150px';
      textarea.style.padding = '10px';
      textarea.style.backgroundColor = '#333';
      textarea.style.color = '#fff';
      textarea.style.border = '1px solid #555';
      textarea.style.borderRadius = '4px';
      textarea.style.resize = 'none';
      modal.appendChild(textarea);

      // Добавляем кнопки действий
      const buttonContainer = document.createElement('div');
      buttonContainer.style.display = 'flex';
      buttonContainer.style.justifyContent = 'space-between';
      buttonContainer.style.marginTop = '15px';

      // Кнопка копирования
      const copyButton = document.createElement('button');
      copyButton.textContent = 'Копировать';
      copyButton.style.padding = '8px 15px';
      copyButton.style.backgroundColor = '#4CAF50';
      copyButton.style.border = 'none';
      copyButton.style.borderRadius = '4px';
      copyButton.style.color = '#fff';
      copyButton.style.cursor = 'pointer';
      copyButton.onclick = function() {
        textarea.select();
        try {
          const successful = document.execCommand('copy');
          if (successful) {
            showPopup('Текст скопирован!');
          } else {
            showPopup('Не удалось скопировать. Выделите и используйте Ctrl+C.');
          }
        } catch (err) {
          showPopup('Ошибка при копировании текста.');
        }
      };
      buttonContainer.appendChild(copyButton);

      // Кнопка перехода в чат
      if (chatId) {
        const goChatButton = document.createElement('button');
        goChatButton.textContent = `Перейти в чат ${chatName}`;
        goChatButton.style.padding = '8px 15px';
        goChatButton.style.backgroundColor = '#2196F3';
        goChatButton.style.border = 'none';
        goChatButton.style.borderRadius = '4px';
        goChatButton.style.color = '#fff';
        goChatButton.style.cursor = 'pointer';
        goChatButton.onclick = function() {
          // Сохраняем текст для автоматической вставки
          localStorage.setItem('pendingForwardText', text);

          // Перед переходом закрываем multiPrompt если открыт
          if (multiSelectionContainer && multiSelectionContainer.style.display !== 'none') {
            clearMultiSelection();
          }

          // Переходим в чат
          window.location.href = `https://web.telegram.org/k/#${chatId}`;

          // Закрываем модальное окно
          document.body.removeChild(modal);
        };
        buttonContainer.appendChild(goChatButton);
      }

      // Кнопка закрытия
      const closeButton = document.createElement('button');
      closeButton.textContent = 'Закрыть';
      closeButton.style.padding = '8px 15px';
      closeButton.style.backgroundColor = '#555';
      closeButton.style.border = 'none';
      closeButton.style.borderRadius = '4px';
      closeButton.style.color = '#fff';
      closeButton.style.cursor = 'pointer';
      closeButton.onclick = function() {
        document.body.removeChild(modal);
      };
      buttonContainer.appendChild(closeButton);

      modal.appendChild(buttonContainer);

      // Добавляем модальное окно в DOM
      document.body.appendChild(modal);

      // Выделяем весь текст
      textarea.select();
    }

    function findInputField() {
      const selectors = [
        '.composer-rich-textarea', // Новый Telegram Web
        '.input-message-input', // Старый Telegram Web
        '[contenteditable="true"]', // Общий случай для редактируемых полей
        'textarea.composer-textarea', // Еще вариант Telegram
        'div.composer-rich-textarea[role="textbox"]', // Еще один вариант
        '.message-input', // Общий случай
        'div[role="textbox"]', // Общий contenteditable div с ролью
        'textarea' // Крайний случай - просто ищем текстовое поле
      ];

      // Пробуем найти элемент по селекторам
      for (const selector of selectors) {
        const elements = document.querySelectorAll(selector);
        for (const element of elements) {
          // Проверяем видимость и доступность элемента
          if (element &&
              window.getComputedStyle(element).display !== 'none' &&
              window.getComputedStyle(element).visibility !== 'hidden' &&
              element.offsetParent !== null) {
            return element;
          }
        }
      }

      // Если ничего не нашли, возвращаем текущее активное поле
      if (document.activeElement &&
          (document.activeElement.tagName === 'TEXTAREA' ||
          document.activeElement.getAttribute('contenteditable') === 'true')) {
        return document.activeElement;
      }

      // Ничего не нашли
      return null;
    }

    // Ensure the summaryPromptTextarea's keydown events don't propagate
    setInterval(() => {
      const summaryPromptTextarea = document.getElementById("summaryPromptTextarea");
      if (summaryPromptTextarea && !summaryPromptTextarea._eventsAttached) {
        summaryPromptTextarea._eventsAttached = true;

        // Add a more powerful event blocking mechanism
        summaryPromptTextarea.addEventListener("keydown", function(e) {
          e.stopPropagation();
          e.stopImmediatePropagation();
          console.log("Blocked propagation for key in summaryPromptTextarea:", e.key);
        }, true);

        // Ensure the textarea maintains focus when typing
        summaryPromptTextarea.addEventListener("keypress", function(e) {
          e.stopPropagation();
          e.stopImmediatePropagation();
        }, true);

        // Add the same for input events
        summaryPromptTextarea.addEventListener("input", function(e) {
          e.stopPropagation();
          console.log("Input in summaryPromptTextarea:", this.value.substring(0, 50) + "...");
        }, true);

        console.log("Added full event protection to summaryPromptTextarea");
      }
    }, 1000);

    const modelDisplay = document.getElementById("modelDisplay");
    const chatModelDisplay = document.getElementById("chatModelDisplay");
    const summaryModelDisplay = document.getElementById("summaryModelDisplay");
    const askChatModelDisplay = document.getElementById("askChatModelDisplay");

    // Common models list
    const commonModels = [
      "gpt-4.1-mini",
      "gpt-4.1-preview",
      "gpt-4",
      "gpt-4-turbo",
      "gpt-3.5-turbo"
    ];

    // Современные фолдауты уже определены в основных стилях выше

    // Современная функция для создания фолдаута на основе details/summary
    function createFoldout() {
      const chatNoteDiv = document.getElementById('chatNote');
      if (!chatNoteDiv) return;

      const selection = window.getSelection();
      if (!selection.rangeCount) {
        showPopup("Сначала выделите текст");
        return;
      }

      const selectedText = selection.toString().trim();
      if (!selectedText) {
        showPopup("Выделите текст для создания фолдаута");
        return;
      }

      // Разделяем на заголовок и содержимое
      const lines = selectedText.split('\n');
      const title = lines[0] || 'Фолдаут';
      const content = lines.length > 1 ? lines.slice(1).join('\n').trim() : '';

      // Создаем современный фолдаут на основе details/summary
      // ВАЖНО: summary НЕ редактируется (contenteditable="false") и ВСЕГДА сворачивает/разворачивает
      const foldoutHTML = `<details class="tg-foldout"><summary contenteditable="false">${title}</summary><div class="tg-foldout-content" contenteditable="true">${content || 'Содержимое фолдаута'}</div></details>`;

      // Получаем range для вставки
      const range = selection.getRangeAt(0);
      range.deleteContents();

      // Создаем и вставляем фолдаут
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = foldoutHTML.trim();
      const foldoutElement = tempDiv.firstChild;

      range.insertNode(foldoutElement);

      // Создаем пустой текстовый узел после фолдаута для возможности ввода
      const textNodeAfter = document.createTextNode('\n');
      range.setStartAfter(foldoutElement);
      range.insertNode(textNodeAfter);

      // Устанавливаем курсор после фолдаута
      range.setStartAfter(textNodeAfter);
      range.setEndAfter(textNodeAfter);
      selection.removeAllRanges();
      selection.addRange(range);

      // Настраиваем обработчики событий
      const summaryElement = foldoutElement.querySelector('summary');
      const contentElement = foldoutElement.querySelector('.tg-foldout-content');

      // ПОЛНОСТЬЮ ОТКЛЮЧАЕМ стандартное поведение details
      foldoutElement.addEventListener('toggle', function(e) {
          e.preventDefault();
          e.stopPropagation();
          return false;
      });

      if (summaryElement) {
          // Заголовок НИКОГДА не редактируется
          summaryElement.addEventListener('keydown', function(e) {
              // Разрешаем навигационные клавиши, но блокируем ввод текста
              if (e.key.length === 1 || e.key === 'Backspace' || e.key === 'Delete' || e.key === 'Enter') {
                  e.preventDefault();
              }
          });

          summaryElement.addEventListener('input', function(e) {
              e.preventDefault();
          });

          summaryElement.addEventListener('paste', function(e) {
              e.preventDefault();
          });

          // СОБСТВЕННАЯ логика сворачивания/разворачивания ТОЛЬКО при клике на заголовок
          summaryElement.addEventListener('click', function(e) {
              e.preventDefault();
              e.stopPropagation();

              // Переключаем состояние фолдаута
              const isOpen = foldoutElement.hasAttribute('open');
              if (isOpen) {
                  foldoutElement.removeAttribute('open');
                  contentElement.style.display = 'none';
              } else {
                  foldoutElement.setAttribute('open', '');
                  contentElement.style.display = 'block';
              }

              return false;
          });
      }

      if (contentElement) {
          // Убеждаемся, что содержимое действительно редактируемое
          contentElement.setAttribute('contenteditable', 'true');

          // ПОЛНАЯ блокировка всех событий, которые могут привести к сворачиванию
          const blockEvent = function(e) {
              e.stopPropagation();
              e.stopImmediatePropagation();
          };

          contentElement.addEventListener('click', blockEvent);
          contentElement.addEventListener('mousedown', blockEvent);
          contentElement.addEventListener('mouseup', blockEvent);
          contentElement.addEventListener('focus', blockEvent);
          contentElement.addEventListener('blur', blockEvent);
          contentElement.addEventListener('keydown', blockEvent);
          contentElement.addEventListener('keyup', blockEvent);
          contentElement.addEventListener('input', blockEvent);
      }

      // Сохраняем изменения
      chatNoteDiv.dispatchEvent(new Event('input', { bubbles: true }));
      setTimeout(() => chatNoteDiv.dispatchEvent(new Event('blur')), 100);

      showPopup("Фолдаут создан (свернут)! Кликните по заголовку для разворачивания.");
    }

    // Добавляем обработчик для создания фолдаута
    document.addEventListener('keydown', function(e) {
      // Проверяем, что мы в фокусе chatNote
      const chatNote = document.getElementById('chatNote');
      if (document.activeElement === chatNote) {
        const foldoutShortcut = localStorage.getItem("shortcut-createFoldout") || "Command+Alt+F";
        if (checkShortcutMatch(e, foldoutShortcut)) {
          e.preventDefault();
          createFoldout();
        }
      }
    });

    // Современная функция активации фолдаутов (details/summary работают автоматически)
    function activateFoldouts(element) {
      if (!element) return;

      // Активируем старые фолдауты для совместимости
      element.querySelectorAll('.note-foldable-header').forEach(header => {
        header.onclick = function(e) {
          e.preventDefault();
          e.stopPropagation();
          this.parentElement.classList.toggle('expanded');
          return false;
        };
      });

      // Настраиваем новые details/summary фолдауты
      element.querySelectorAll('.tg-foldout').forEach(foldout => {
        const summaryElement = foldout.querySelector('summary');
        const contentElement = foldout.querySelector('.tg-foldout-content');

        // Проверяем, не активирован ли уже этот фолдаут
        if (foldout._foldoutActivated) {
          return; // Пропускаем уже активированные фолдауты
        }

        // ПОЛНОСТЬЮ ОТКЛЮЧАЕМ стандартное поведение details
        const toggleHandler = function(e) {
          e.preventDefault();
          e.stopPropagation();
          return false;
        };
        foldout.addEventListener('toggle', toggleHandler);

        if (summaryElement) {
          // Убеждаемся, что заголовок НЕ редактируется
          summaryElement.setAttribute('contenteditable', 'false');

          // Предотвращаем редактирование текста
          const keydownHandler = function(e) {
            if (e.key.length === 1 || e.key === 'Backspace' || e.key === 'Delete' || e.key === 'Enter') {
              e.preventDefault();
            }
          };
          summaryElement.addEventListener('keydown', keydownHandler);

          const inputHandler = function(e) {
            e.preventDefault();
          };
          summaryElement.addEventListener('input', inputHandler);

          const pasteHandler = function(e) {
            e.preventDefault();
          };
          summaryElement.addEventListener('paste', pasteHandler);

          // СОБСТВЕННАЯ логика сворачивания/разворачивания ТОЛЬКО при клике на заголовок
          const clickHandler = function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Переключаем состояние фолдаута
            const isOpen = foldout.hasAttribute('open');
            if (isOpen) {
              foldout.removeAttribute('open');
              if (contentElement) contentElement.style.display = 'none';
            } else {
              foldout.setAttribute('open', '');
              if (contentElement) contentElement.style.display = 'block';
            }

            return false;
          };
          summaryElement.addEventListener('click', clickHandler);
        }

        if (contentElement) {
          // Убеждаемся, что содержимое можно редактировать
          contentElement.setAttribute('contenteditable', 'true');

          // ПОЛНАЯ блокировка всех событий, которые могут привести к сворачиванию
          const blockEvent = function(e) {
            e.stopPropagation();
            e.stopImmediatePropagation();
          };

          // Блокируем события, но НЕ блокируем blur полностью
          const events = ['click', 'mousedown', 'mouseup', 'focus', 'keydown', 'keyup', 'input'];
          events.forEach(eventType => {
            contentElement.addEventListener(eventType, blockEvent);
          });

          // Специальный обработчик blur - не блокируем его полностью
          const blurHandler = function(e) {
            e.stopPropagation();
            // Не блокируем blur полностью, чтобы сохранение работало
          };
          contentElement.addEventListener('blur', blurHandler);
        }

        // Помечаем фолдаут как активированный
        foldout._foldoutActivated = true;
      });

      console.log("🔄 Фолдауты активированы:", element.querySelectorAll('.tg-foldout').length);
    }

    // Модифицируем существующий обработчик табов для активации фолдаутов
    const originalTabNotesClick = tabNotesBtn.onclick;
    tabNotesBtn.onclick = function() {
      if (originalTabNotesClick) originalTabNotesClick.call(this);
      setTimeout(() => {
        activateFoldouts(document.getElementById('chatNote'));
      }, 100);
    };

    // Переопределяем обработку hashchange для активации фолдаутов
    const originalHashChange = window.onhashchange;
    window.onhashchange = function() {
      if (originalHashChange) originalHashChange.call(this);
      setTimeout(() => {
        activateFoldouts(document.getElementById('chatNote'));
      }, 100);
    };

    // Исправляем проблему с двойными Enter
    function fixEnterBehavior() {
      const chatNoteDiv = document.getElementById('chatNote');
      if (!chatNoteDiv) return;

      // Удаляем существующие обработчики keydown для Enter
      const oldListeners = chatNoteDiv.getEventListeners ?
                          chatNoteDiv.getEventListeners('keydown') || [] : [];

      for (let i = 0; i < oldListeners.length; i++) {
        const listener = oldListeners[i];
        if (listener._isEnterHandler) {
          chatNoteDiv.removeEventListener('keydown', listener);
        }
      }

      // Создаем новый обработчик для Enter
      const enterHandler = function(e) {
        if (e.key === 'Enter') {
          e.preventDefault();
          e.stopPropagation();

          // Вставка одинарного переноса строки
          document.execCommand('insertText', false, '\n');

          return false;
        }
      };

      // Помечаем наш обработчик
      enterHandler._isEnterHandler = true;

      // Добавляем обработчик с высоким приоритетом capture=true
      chatNoteDiv.addEventListener('keydown', enterHandler, true);

      console.log("🔄 Обработчик Enter обновлен");
    }

    // Вызываем функцию сразу и при загрузке DOM
    fixEnterBehavior();
    document.addEventListener('DOMContentLoaded', fixEnterBehavior);

    // Сохраняем оригинальный обработчик вкладки
    const notesTabOriginalClick = tabNotesBtn.onclick;

    // Заменяем обработчик полностью новым
    tabNotesBtn.onclick = function() {
      // Активируем вкладку стандартным способом
      activateTab(tabNotesBtn, tabNotesContent);

      // Применяем наши улучшения с задержкой
      setTimeout(() => {
        activateFoldouts(document.getElementById('chatNote'));
        fixEnterBehavior();
      }, 100);
    };

    // Современные фолдауты используют стили .tg-foldout, определенные выше

    // Обработчик клавиш для создания фолдаутов
    document.addEventListener('keydown', function(e) {
      const foldoutShortcut = localStorage.getItem("shortcut-createFoldout") || "Command+Alt+F";
      if (checkShortcutMatch(e, foldoutShortcut)) {
        const chatNote = document.getElementById('chatNote');
        if (document.activeElement === chatNote) {
          e.preventDefault();
          const selection = window.getSelection();
          if (selection.toString().trim()) {
            createFoldout();
          } else {
            showPopup("Выделите текст для создания фолдаута");
          }
        }
      }
    });

    // Удалена старая функция createSimpleFoldout - используем createFoldout()

    // Упрощенная функция активации фолдаутов (details/summary работают автоматически)
    function activateAllFoldouts() {
      const chatNote = document.getElementById('chatNote');
      if (!chatNote) return;

      // Активируем только старые фолдауты для совместимости
      const noteFoldHeaders = chatNote.querySelectorAll('.note-foldable-header');
      noteFoldHeaders.forEach(header => {
        header.onclick = function(e) {
          e.preventDefault();
          e.stopPropagation();
          this.parentElement.classList.toggle('expanded');
          return false;
        };
      });

      // Настраиваем новые .tg-foldout (details/summary) фолдауты
      const newFoldouts = chatNote.querySelectorAll('.tg-foldout');
      newFoldouts.forEach(foldout => {
        const summaryElement = foldout.querySelector('summary');
        const contentElement = foldout.querySelector('.tg-foldout-content');

        // Проверяем, не активирован ли уже этот фолдаут
        if (foldout._foldoutActivated) {
          return; // Пропускаем уже активированные фолдауты
        }

        // ПОЛНОСТЬЮ ОТКЛЮЧАЕМ стандартное поведение details
        const toggleHandler = function(e) {
          e.preventDefault();
          e.stopPropagation();
          return false;
        };
        foldout.addEventListener('toggle', toggleHandler);

        if (summaryElement) {
          // Убеждаемся, что заголовок НЕ редактируется
          summaryElement.setAttribute('contenteditable', 'false');

          // Предотвращаем редактирование текста
          const keydownHandler = function(e) {
            if (e.key.length === 1 || e.key === 'Backspace' || e.key === 'Delete' || e.key === 'Enter') {
              e.preventDefault();
            }
          };
          summaryElement.addEventListener('keydown', keydownHandler);

          const inputHandler = function(e) {
            e.preventDefault();
          };
          summaryElement.addEventListener('input', inputHandler);

          const pasteHandler = function(e) {
            e.preventDefault();
          };
          summaryElement.addEventListener('paste', pasteHandler);

          // СОБСТВЕННАЯ логика сворачивания/разворачивания ТОЛЬКО при клике на заголовок
          const clickHandler = function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Переключаем состояние фолдаута
            const isOpen = foldout.hasAttribute('open');
            if (isOpen) {
              foldout.removeAttribute('open');
              if (contentElement) contentElement.style.display = 'none';
            } else {
              foldout.setAttribute('open', '');
              if (contentElement) contentElement.style.display = 'block';
            }

            return false;
          };
          summaryElement.addEventListener('click', clickHandler);
        }

        if (contentElement) {
          // Убеждаемся, что содержимое можно редактировать
          contentElement.setAttribute('contenteditable', 'true');

          // ПОЛНАЯ блокировка всех событий, которые могут привести к сворачиванию
          const blockEvent = function(e) {
            e.stopPropagation();
            e.stopImmediatePropagation();
          };

          // Блокируем события, но НЕ блокируем blur полностью
          const events = ['click', 'mousedown', 'mouseup', 'focus', 'keydown', 'keyup', 'input'];
          events.forEach(eventType => {
            contentElement.addEventListener(eventType, blockEvent);
          });

          // Специальный обработчик blur - не блокируем его полностью
          const blurHandler = function(e) {
            e.stopPropagation();
            // Не блокируем blur полностью, чтобы сохранение работало
          };
          contentElement.addEventListener('blur', blurHandler);
        }

        // Помечаем фолдаут как активированный
        foldout._foldoutActivated = true;
      });

      console.log("🔄 Активировано фолдаутов: старых=" + noteFoldHeaders.length +
                  ", новых=" + newFoldouts.length);
    }

    // Добавляем обработчики для гарантированной активации фолдаутов
    document.addEventListener('DOMContentLoaded', activateAllFoldouts);

  // Добавляем кнопку "скрепка" для привязки URL к выделенному тексту
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      const notesTab = document.getElementById('tab-notes-content');
      if (notesTab) {
        // Проверяем, не существует ли уже кнопка
        if (document.querySelector('.note-url-button')) return;

        const buttonContainer = document.createElement('div');
        buttonContainer.style.position = 'absolute';
        buttonContainer.style.top = '5px';
        buttonContainer.style.right = '10px';
        buttonContainer.style.zIndex = '1000';

        const urlButton = document.createElement('button');
        urlButton.innerHTML = '📎';
        urlButton.className = 'note-url-button';
        urlButton.title = 'Привязать URL к выделенному тексту';
        urlButton.style.background = 'transparent';
        urlButton.style.border = 'none';
        urlButton.style.color = '#fff';
        urlButton.style.fontSize = '18px';
        urlButton.style.cursor = 'pointer';
        urlButton.style.padding = '5px';
        urlButton.style.borderRadius = '3px';

        urlButton.addEventListener('mouseover', () => {
          urlButton.style.background = 'rgba(255, 255, 255, 0.1)';
        });

        urlButton.addEventListener('mouseout', () => {
          urlButton.style.background = 'transparent';
        });

        urlButton.addEventListener('click', () => {
          const selection = window.getSelection();
          if (selection.toString().trim()) {
            attachUrlToSelection(selection);
          } else {
            showPopup("Выделите текст для привязки URL");
          }
        });

        buttonContainer.appendChild(urlButton);
        notesTab.appendChild(buttonContainer);
      }
    }, 1000); // Задержка для уверенности, что все элементы загружены
  });
    window.addEventListener('load', activateAllFoldouts);
    window.addEventListener('hashchange', () => {
      setTimeout(activateAllFoldouts, 300);
    });



    // При каждой навигации внутри приложения
    const originalPushState = history.pushState;
    history.pushState = function() {
      originalPushState.apply(this, arguments);
      setTimeout(activateAllFoldouts, 300);
    };

    const originalReplaceState = history.replaceState;
    history.replaceState = function() {
      originalReplaceState.apply(this, arguments);
      setTimeout(activateAllFoldouts, 300);
    };

    // Современные фолдауты настроены автоматически

   // Добавляем MutationObserver для мониторинга изменений в DOM
   function setupFoldoutObserver() {
    // Находим контейнер, в котором могут появляться заметки
    const appContainer = document.querySelector('.tgme_page_wrap') || document.body;

    // Создаем наблюдатель за изменениями в DOM
    const observer = new MutationObserver((mutations) => {
      // Проверяем, есть ли chatNote в DOM
      const chatNote = document.getElementById('chatNote');
      if (chatNote) {
        // Активируем все фолдауты
        activateAllFoldouts();

        // Также активируем стандартные фолдауты
        activateFoldouts(chatNote);
      }
    });

    // Настраиваем наблюдение за изменениями в DOM
    observer.observe(appContainer, {
      childList: true,
      subtree: true
    });

    // При переходе между чатами (изменении URL) также активировать фолдауты
    window.addEventListener('popstate', () => {
      setTimeout(() => {
        const chatNote = document.getElementById('chatNote');
        if (chatNote) {
          activateAllFoldouts();
          activateFoldouts(chatNote);
        }
      }, 500);
    });

    console.log("🔄 Наблюдатель за фолдаутами установлен");
  }

  // Запускаем наблюдатель
  setupFoldoutObserver();

  document.addEventListener('keydown', function(e) {
    const chatNote = document.getElementById('chatNote');
    if (document.activeElement === chatNote) {
      if (e.key === 'u' && (e.metaKey || e.ctrlKey) && !e.shiftKey && !e.altKey) {
        e.preventDefault();
        const selection = window.getSelection();
        if (selection.toString().trim()) {
          createFoldout();
        } else {
          showPopup("Выделите текст для создания фолдаута");
        }
      }


      // Используем настраиваемый шорткат для привязки URL
      const attachUrlShortcut = localStorage.getItem('shortcut-attachUrl') || 'Command+L';
      if (checkShortcutMatch(e, attachUrlShortcut)) {
        e.preventDefault();
        const selection = window.getSelection();
        if (selection.toString().trim()) {
          attachUrlToSelection(selection);
        } else {
          showPopup("Выделите текст для привязки URL");
        }
      }
    }
  });

  // Функция для привязки URL к выделенному тексту
  function attachUrlToSelection(selection) {
    if (!selection.rangeCount) return;

    const text = selection.toString().trim();
    if (!text) {
      showPopup("Выделите текст для привязки URL");
      return;
    }

    // Запрос URL у пользователя
    const url = prompt("Введите URL для привязки к выделенному тексту:", "https://");
    if (!url || !url.trim()) {
      return;
    }

    const range = selection.getRangeAt(0);
    range.deleteContents();

    // Создаем элемент ссылки
    const linkElement = document.createElement("a");
    linkElement.href = url;
    linkElement.target = "_blank";
    linkElement.className = "note-url-link";
    linkElement.textContent = text;
    linkElement.style.color = "#4d9cf8";
    linkElement.style.textDecoration = "underline";
    linkElement.style.cursor = "pointer";
    linkElement.title = url;
    linkElement.dataset.url = url;

    // Вставляем ссылку
    range.insertNode(linkElement);

    // Добавляем обработчик клика для открытия URL
    linkElement.addEventListener("click", function(e) {
      e.preventDefault();
      e.stopPropagation();
      window.open(this.dataset.url, '_blank');
    });

    // Сохраняем изменения
    const chatNote = document.getElementById("chatNote");
    if (chatNote) {
      chatNote.dispatchEvent(new Event("input", { bubbles: true }));
      setTimeout(() => chatNote.dispatchEvent(new Event("blur")), 100);
    }

    showPopup("URL привязан к тексту");
  }
// Function to create and show model selection menu
function showModelSelectionMenu(event, targetElement) {
  event.preventDefault();
  event.stopPropagation();

  // Remove any existing model menu
  const existingMenu = document.getElementById('model-selection-menu');
  if (existingMenu) {
    existingMenu.remove();
  }

  // Create menu
  const menuDiv = document.createElement('div');
  menuDiv.id = 'model-selection-menu';
  menuDiv.style.position = 'absolute';
  menuDiv.style.zIndex = '11001';
  menuDiv.style.backgroundColor = '#2a2a2a';
  menuDiv.style.border = '1px solid #444';
  menuDiv.style.borderRadius = '6px';
  menuDiv.style.padding = '6px 0';
  menuDiv.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
  menuDiv.style.minWidth = '150px';
  menuDiv.style.maxHeight = '300px';
  menuDiv.style.overflowY = 'auto';

  // Position menu near the clicked element
  const rect = targetElement.getBoundingClientRect();
  menuDiv.style.left = rect.left + 'px';
  menuDiv.style.top = (rect.top + rect.height) + 'px';

  // Add model options
  commonModels.forEach(model => {
    const modelOption = document.createElement('div');
    modelOption.textContent = model;
    modelOption.style.padding = '8px 12px';
    modelOption.style.cursor = 'pointer';
    modelOption.style.color = '#fff';
    modelOption.style.fontSize = '14px';

    // Highlight if this is the current selection
    if (model === targetElement.textContent) {
      modelOption.style.backgroundColor = '#3a539b';
      modelOption.style.fontWeight = 'bold';
    }

    modelOption.addEventListener('mouseover', () => {
      modelOption.style.backgroundColor = modelOption.style.fontWeight === 'bold' ? '#3a539b' : '#444';
    });

    modelOption.addEventListener('mouseout', () => {
      modelOption.style.backgroundColor = modelOption.style.fontWeight === 'bold' ? '#3a539b' : 'transparent';
    });

    modelOption.addEventListener('click', () => {
      targetElement.textContent = model;
      menuDiv.remove();
    });

    menuDiv.appendChild(modelOption);
  });

  // Add a custom option
  const customOption = document.createElement('div');
  customOption.textContent = 'Custom...';
  customOption.style.padding = '8px 12px';
  customOption.style.cursor = 'pointer';
  customOption.style.color = '#fff';
  customOption.style.fontSize = '14px';
  customOption.style.borderTop = '1px solid #444';
  customOption.style.marginTop = '6px';

  customOption.addEventListener('mouseover', () => {
    customOption.style.backgroundColor = '#444';
  });

  customOption.addEventListener('mouseout', () => {
    customOption.style.backgroundColor = 'transparent';
  });

  customOption.addEventListener('click', () => {
    const customModel = prompt('Enter custom model name:', targetElement.textContent);
    if (customModel && customModel.trim()) {
      targetElement.textContent = customModel.trim();
    }
    menuDiv.remove();
  });

  menuDiv.appendChild(customOption);
  document.body.appendChild(menuDiv);

  // Close menu when clicking outside
  setTimeout(() => {
    document.addEventListener('click', function closeMenu(e) {
      if (!menuDiv.contains(e.target) && e.target !== targetElement) {
        menuDiv.remove();
        document.removeEventListener('click', closeMenu);
      }
    });
  }, 0);
}

// Add context menu to model displays
if (modelDisplay) {
  modelDisplay.addEventListener('contextmenu', (e) => showModelSelectionMenu(e, modelDisplay));
  modelDisplay.addEventListener('click', (e) => showModelSelectionMenu(e, modelDisplay));
}

if (chatModelDisplay) {
  chatModelDisplay.addEventListener('contextmenu', (e) => showModelSelectionMenu(e, chatModelDisplay));
  chatModelDisplay.addEventListener('click', (e) => showModelSelectionMenu(e, chatModelDisplay));
}

if (summaryModelDisplay) {
  summaryModelDisplay.addEventListener('contextmenu', (e) => showModelSelectionMenu(e, summaryModelDisplay));
  summaryModelDisplay.addEventListener('click', (e) => showModelSelectionMenu(e, summaryModelDisplay));
}

if (askChatModelDisplay) {
  askChatModelDisplay.addEventListener('contextmenu', (e) => showModelSelectionMenu(e, askChatModelDisplay));
  askChatModelDisplay.addEventListener('click', (e) => showModelSelectionMenu(e, askChatModelDisplay));
}




function showFavoriteChatManager() {
  // Удаляем предыдущее окно, если оно есть
  const existingManager = document.getElementById('favorite-chats-manager');
  if (existingManager) existingManager.remove();
  
  // Получаем список избранных чатов
  const favoriteChats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
  
  // Добавляем order для старых записей, если его нет
  favoriteChats.forEach((chat, index) => {
    if (chat.order === undefined) {
      chat.order = index * 10; // Используем шаг 10 для удобства вставки между элементами
    }
  });
  
  // Сортируем чаты по порядку
  favoriteChats.sort((a, b) => a.order - b.order);
  
  // Сохраняем обновленный список
  localStorage.setItem('favorite_chats', JSON.stringify(favoriteChats));
  
  // Создаем окно управления
  const manager = document.createElement('div');
  manager.id = 'favorite-chats-manager';
  manager.style.position = 'fixed';
  manager.style.left = '50%';
  manager.style.top = '50%';
  manager.style.transform = 'translate(-50%, -50%)';
  manager.style.backgroundColor = '#1A1A1A';
  manager.style.border = '1px solid #444';
  manager.style.borderRadius = '8px';
  manager.style.padding = '16px';
  manager.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.6)';
  manager.style.zIndex = '10001';
  manager.style.width = '400px';
  manager.style.maxHeight = '80vh';
  manager.style.overflowY = 'auto';
  
  // Добавляем заголовок
  const header = document.createElement('div');
  header.innerText = 'Управление избранными чатами';
  header.style.fontSize = '18px';
  header.style.fontWeight = 'bold';
  header.style.marginBottom = '16px';
  header.style.color = '#fff';
  header.style.borderBottom = '1px solid #444';
  header.style.paddingBottom = '8px';
  manager.appendChild(header);
  
  // Создаем контейнер для списка чатов
  const chatsList = document.createElement('div');
  chatsList.id = 'favorite-chats-list';
  chatsList.style.marginBottom = '16px';
  manager.appendChild(chatsList);
  
  // Функция для отрисовки списка чатов
  function renderChatsList() {
    chatsList.innerHTML = '';
    const chats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
    
    // Добавляем order если его нет и сортируем
    chats.forEach((chat, index) => {
      if (chat.order === undefined) {
        chat.order = index * 10;
      }
    });
    
    // Сортируем только по порядку
    chats.sort((a, b) => {
      return a.order - b.order;
    });
    
    // Сохраняем отсортированный список
    localStorage.setItem('favorite_chats', JSON.stringify(chats));
    
    if (chats.length === 0) {
      const emptyState = document.createElement('div');
      emptyState.innerText = 'Нет избранных чатов';
      emptyState.style.padding = '8px';
      emptyState.style.color = '#888';
      emptyState.style.textAlign = 'center';
      chatsList.appendChild(emptyState);
    }
    
    chats.forEach((chat, index) => {
      const item = document.createElement('div');
      item.style.display = 'flex';
      item.style.justifyContent = 'space-between';
      item.style.alignItems = 'center';
      item.style.padding = '8px';
      item.style.borderBottom = '1px solid #333';
      
      const chatInfo = document.createElement('div');
      chatInfo.innerHTML = `<div style="font-weight: bold;">${chat.name}</div>
                          <div style="font-size: 12px; color: #888;">ID: ${chat.id}</div>`;
      item.appendChild(chatInfo);
      
      const actions = document.createElement('div');
      actions.style.display = 'flex';
      actions.style.gap = '8px';
      
      // Добавляем кнопки для изменения порядка для всех чатов
      // Кнопка вверх
      const upButton = document.createElement('button');
      upButton.innerText = '↑';
      upButton.title = 'Переместить выше';
      upButton.style.background = 'none';
      upButton.style.border = 'none';
      upButton.style.fontSize = '16px';
      upButton.style.cursor = 'pointer';
      upButton.addEventListener('click', () => {
        moveChat(index, -1);
      });
      
      // Кнопка вниз
      const downButton = document.createElement('button');
      downButton.innerText = '↓';
      downButton.title = 'Переместить ниже';
      downButton.style.background = 'none';
      downButton.style.border = 'none';
      downButton.style.fontSize = '16px';
      downButton.style.cursor = 'pointer';
      downButton.addEventListener('click', () => {
        moveChat(index, 1);
      });
      
      // Добавляем кнопки позиционирования
      actions.appendChild(upButton);
      actions.appendChild(downButton);
      
      const editButton = document.createElement('button');
      editButton.innerText = '✏️';
      editButton.title = 'Редактировать';
      editButton.style.background = 'none';
      editButton.style.border = 'none';
      editButton.style.fontSize = '16px';
      editButton.style.cursor = 'pointer';
      editButton.addEventListener('click', () => {
        editFavoriteChat(index);
      });
      actions.appendChild(editButton);
      
      const deleteButton = document.createElement('button');
      deleteButton.innerText = '🗑️';
      deleteButton.title = 'Удалить';
      deleteButton.style.background = 'none';
      deleteButton.style.border = 'none';
      deleteButton.style.fontSize = '16px';
      deleteButton.style.cursor = 'pointer';
      deleteButton.addEventListener('click', () => {
        deleteFavoriteChat(index);
        renderChatsList();
      });
      actions.appendChild(deleteButton);
      
      item.appendChild(actions);
      chatsList.appendChild(item);
    });
  }
  
  // Функция для перемещения чата вверх или вниз в списке
  function moveChat(index, direction) {
    const chats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
    
    // Проверяем возможность перемещения
    if ((direction < 0 && index === 0) || 
        (direction > 0 && index === chats.length - 1)) {
      // Достигнут край списка
      return;
    }
    
    // Меняем порядок чатов
    const newIndex = index + direction;
    if (newIndex >= 0 && newIndex < chats.length) {
      // Меняем местами порядок
      const temp = chats[index].order;
      chats[index].order = chats[newIndex].order;
      chats[newIndex].order = temp;
      
      // Сохраняем и обновляем список
      localStorage.setItem('favorite_chats', JSON.stringify(chats));
      renderChatsList();
    }
  }
  
  // Первоначальная отрисовка списка
  renderChatsList();
  
  // Функция для редактирования чата
  function editFavoriteChat(index) {
    const chats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
    const chat = chats[index];
    
    const nameInput = document.createElement('input');
    nameInput.type = 'text';
    nameInput.value = chat.name;
    nameInput.placeholder = 'Название чата';
    nameInput.style.width = '100%';
    nameInput.style.padding = '8px';
    nameInput.style.marginBottom = '8px';
    nameInput.style.backgroundColor = '#333';
    nameInput.style.border = '1px solid #444';
    nameInput.style.borderRadius = '4px';
    nameInput.style.color = '#fff';
    
    const idInput = document.createElement('input');
    idInput.type = 'text';
    idInput.value = chat.id;
    idInput.placeholder = 'ID чата';
    idInput.style.width = '100%';
    idInput.style.padding = '8px';
    idInput.style.marginBottom = '16px';
    idInput.style.backgroundColor = '#333';
    idInput.style.border = '1px solid #444';
    idInput.style.borderRadius = '4px';
    idInput.style.color = '#fff';
    
    const saveButton = document.createElement('button');
    saveButton.innerText = 'Сохранить';
    saveButton.style.padding = '8px 16px';
    saveButton.style.backgroundColor = '#4CAF50';
    saveButton.style.border = 'none';
    saveButton.style.borderRadius = '4px';
    saveButton.style.color = '#fff';
    saveButton.style.cursor = 'pointer';
    saveButton.style.marginRight = '8px';
    
    const cancelButton = document.createElement('button');
    cancelButton.innerText = 'Отмена';
    cancelButton.style.padding = '8px 16px';
    cancelButton.style.backgroundColor = '#555';
    cancelButton.style.border = 'none';
    cancelButton.style.borderRadius = '4px';
    cancelButton.style.color = '#fff';
    cancelButton.style.cursor = 'pointer';
    
    const buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.justifyContent = 'flex-end';
    buttonContainer.appendChild(saveButton);
    buttonContainer.appendChild(cancelButton);
    
    const editForm = document.createElement('div');
    editForm.appendChild(nameInput);
    editForm.appendChild(idInput);
    editForm.appendChild(buttonContainer);
    
    chatsList.innerHTML = '';
    chatsList.appendChild(editForm);
    
    saveButton.addEventListener('click', () => {
      if (!nameInput.value.trim() || !idInput.value.trim()) {
        showPopup('Введите имя и ID чата');
        return;
      }
      
      chats[index] = {
        name: nameInput.value.trim(),
        id: idInput.value.trim(),
        order: chat.order || index * 10
      };
      
      localStorage.setItem('favorite_chats', JSON.stringify(chats));
      renderChatsList();
      showPopup('Чат обновлен');
    });
    
    cancelButton.addEventListener('click', () => {
      renderChatsList();
    });
  }
  
  // Функция для удаления чата
  function deleteFavoriteChat(index) {
    const chats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
    chats.splice(index, 1);
    localStorage.setItem('favorite_chats', JSON.stringify(chats));
    showPopup('Чат удален');
  }
  
  // Добавляем форму для добавления нового чата
  const addForm = document.createElement('div');
  addForm.style.marginTop = '16px';
  addForm.style.borderTop = '1px solid #444';
  addForm.style.paddingTop = '16px';
  
  const formHeader = document.createElement('div');
  formHeader.innerText = 'Добавить новый чат';
  formHeader.style.fontWeight = 'bold';
  formHeader.style.marginBottom = '8px';
  formHeader.style.color = '#fff';
  addForm.appendChild(formHeader);
  
  const nameInput = document.createElement('input');
  nameInput.type = 'text';
  nameInput.id = 'new-chat-name';
  nameInput.placeholder = 'Название чата';
  nameInput.style.width = '100%';
  nameInput.style.padding = '8px';
  nameInput.style.marginBottom = '8px';
  nameInput.style.backgroundColor = '#333';
  nameInput.style.border = '1px solid #444';
  nameInput.style.borderRadius = '4px';
  nameInput.style.color = '#fff';
  addForm.appendChild(nameInput);
  
  const idInput = document.createElement('input');
  idInput.type = 'text';
  idInput.id = 'new-chat-id';
  idInput.placeholder = 'ID чата';
  idInput.style.width = '100%';
  idInput.style.padding = '8px';
  idInput.style.marginBottom = '16px';
  idInput.style.backgroundColor = '#333';
  idInput.style.border = '1px solid #444';
  idInput.style.borderRadius = '4px';
  idInput.style.color = '#fff';
  addForm.appendChild(idInput);
  
  const getCurrentChatButton = document.createElement('button');
  getCurrentChatButton.innerText = 'Использовать текущий чат';
  getCurrentChatButton.style.width = '100%';
  getCurrentChatButton.style.padding = '8px';
  getCurrentChatButton.style.marginBottom = '16px';
  getCurrentChatButton.style.backgroundColor = '#2196F3';
  getCurrentChatButton.style.border = 'none';
  getCurrentChatButton.style.borderRadius = '4px';
  getCurrentChatButton.style.color = '#fff';
  getCurrentChatButton.style.cursor = 'pointer';
  
  getCurrentChatButton.addEventListener('click', () => {
    try {
      // Добавляем индикатор загрузки для обратной связи
      getCurrentChatButton.disabled = true;
      getCurrentChatButton.innerText = 'Получение информации...';
      
      // Получаем ID из URL - простой подход
      const currentUrl = window.location.href;
      let chatId = '';
      let chatName = '';
      
      // Извлекаем всё, что после последнего слеша или решетки
      const urlMatch = currentUrl.match(/[/#]([^/#]+)$/);
      if (urlMatch && urlMatch[1]) {
        chatId = urlMatch[1];
        logger.info('ID чата получен из URL:', chatId);
      } else {
        // Если не удалось найти ID в URL, используем текущее значение глобальной переменной
        chatId = window.chatId || 'chat_' + Math.floor(Math.random() * 1000000);
        logger.info('Используем запасной ID чата:', chatId);
      }
      
      // Получаем имя чата из элемента peer-title
      const peerTitleElement = document.querySelector('.peer-title');
      if (peerTitleElement) {
        chatName = peerTitleElement.textContent.trim();
        logger.info('Имя чата получено из .peer-title:', chatName);
      } else {
        // Если элемент не найден, используем заголовок страницы или ID
        chatName = document.title.replace(' – Telegram', '').replace(' - Telegram', '').trim() || `Чат ${chatId}`;
        logger.info('Запасное имя чата:', chatName);
      }
      
      // Обновляем поля формы
      idInput.value = chatId;
      nameInput.value = chatName;
      
      // Показываем сообщение об успехе
      showPopup(`Информация о чате получена: ${chatName}`);
      
    } catch (error) {
      logger.error('Ошибка при получении информации о чате:', error);
      showPopup('Ошибка при получении информации о чате');
      
      // Устанавливаем значения по умолчанию
      idInput.value = 'chat_' + Math.floor(Math.random() * 1000000);
      nameInput.value = 'Новый чат';
    } finally {
      // Восстанавливаем кнопку
      getCurrentChatButton.disabled = false;
      getCurrentChatButton.innerText = 'Использовать текущий чат';
    }
  });
  
  addForm.appendChild(getCurrentChatButton);
  
  const addButton = document.createElement('button');
  addButton.innerText = 'Добавить';
  addButton.style.width = '100%';
  addButton.style.padding = '8px';
  addButton.style.backgroundColor = '#4CAF50';
  addButton.style.border = 'none';
  addButton.style.borderRadius = '4px';
  addButton.style.color = '#fff';
  addButton.style.cursor = 'pointer';
  
  addButton.addEventListener('click', () => {
    const name = nameInput.value.trim();
    const id = idInput.value.trim();
    
    if (!name || !id) {
      showPopup('Введите имя и ID чата');
      return;
    }
    
    const chats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
    chats.push({ name, id });
    localStorage.setItem('favorite_chats', JSON.stringify(chats));
    
    nameInput.value = '';
    idInput.value = '';
    
    renderChatsList();
    showPopup('Чат добавлен');
  });
  
  addForm.appendChild(addButton);
  manager.appendChild(addForm);
  
  // Добавляем кнопку закрытия
  const closeButton = document.createElement('button');
  closeButton.innerText = 'Закрыть';
  closeButton.style.width = '100%';
  closeButton.style.padding = '8px';
  closeButton.style.marginTop = '16px';
  closeButton.style.backgroundColor = '#555';
  closeButton.style.border = 'none';
  closeButton.style.borderRadius = '4px';
  closeButton.style.color = '#fff';
  closeButton.style.cursor = 'pointer';
  
  closeButton.addEventListener('click', () => {
    manager.remove();
  });
  
  manager.appendChild(closeButton);
  
  // Добавляем окно в DOM
  document.body.appendChild(manager);
  
  // Делаем окно перетаскиваемым
  makeDraggable(manager);
}


    



// Добавляем обработчик для автоматического сохранения при вводе текста
chatNoteDiv.addEventListener("input", () => {
  // Если активен таб, сохраняем в таб, а не в основную заметку
  if (currentSubNoteId) {
    saveCurrentSubNote();
    return;
  }

  // Создаем временный div для обработки текста
  const tempDiv = chatNoteDiv.cloneNode(true);

  // Заменяем все ссылки на их URL
  const linkMap = new Map();
  let index = 0;

  tempDiv.querySelectorAll('a').forEach(a => {
      const placeholder = `__LINK_PLACEHOLDER_${index}__`;
      linkMap.set(placeholder, a.getAttribute('href'));
      a.replaceWith(placeholder);
      index++;
  });

  let fullText = tempDiv.innerHTML
      .replace(/<br>/g, '\n')
      .replace(/<div>/g, '\n')
      .replace(/<\/div>/g, '')
      .replace(/&nbsp;/g, ' ');

  linkMap.forEach((href, placeholder) => {
      fullText = fullText.replace(placeholder, href);
  });

  saveNote(noteCookieName, fullText);
});



let lastFavoritesUIOpenedTime = 0;

//  Флаг для индикации, был ли открыт UI
let favoritesUIJustOpened = false;

// Храним время последнего открытия UI избранных чатов
let lastFavoriteUIOpenTimestamp = 0;

// Функция для сохранения и загрузки общих заметок
function saveGeneralNotes(notes, tabName = 'main') {
// Сокращаем ссылки перед сохранением
notes = shortenLinks(notes);
localStorage.setItem(`telegram_general_notes_${tabName}`, notes);
}

function loadGeneralNotes(tabName = 'main') {
return localStorage.getItem(`telegram_general_notes_${tabName}`) || '';
}

// Функция для сокращения ссылок в тексте
function shortenLinks(text) {
if (!text) return '';

// Регулярное выражение для поиска ссылок
const urlRegex = /(https?:\/\/[^\s]+)/g;

// Заменяем каждую найденную ссылку на сокращенную версию
return text.replace(urlRegex, function(url) {
  try {
    // Создаем объект URL для корректного разбора
    const parsedUrl = new URL(url);
    
    // Получаем хост и путь
    const host = parsedUrl.hostname;
    const path = parsedUrl.pathname;
    
    // Если путь слишком длинный, сокращаем его
    const shortPath = path.length > 15 ? path.substring(0, 12) + '...' : path;
    
    // Создаем сокращенный текст для отображения
    const displayText = host + (shortPath !== '/' ? shortPath : '');
    
    // Создаем HTML-ссылку, которая будет кликабельной
    return `<a href="${url}" target="_blank" style="color: #4b6cb7; text-decoration: underline;">${displayText}</a>`;
  } catch (e) {
    // В случае ошибки разбора URL, возвращаем исходную ссылку
    return `<a href="${url}" target="_blank" style="color: #4b6cb7; text-decoration: underline;">${url}</a>`;
  }
});
}


// Глобальная переменная для отслеживания времени создания UI избранных чатов


let favoriteChatsUICreatedTime = 0;

// Флаг для временного отключения логики закрытия UI (используется для предотвращения закрытия сразу после открытия)
let favoriteChatsUIDisableClose = false;

// Функция UI избранных чатов с превью заметок

function showFavoriteChatsWithNotes() {
// Проверяем существующий UI
const existingUI = document.getElementById('favorite-chats-notes-ui');
if (existingUI) {
  // Save the active tab before removing
  localStorage.setItem('telegram_last_active_tab', activeTab);
  if (document.body.contains(existingUI)) {
    existingUI.remove();
  }
  ensureInputUnblocked(); // Ensure input is unblocked
  return;
}
      
const creationTime = Date.now();

const favoriteChats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');

if (favoriteChats.length === 0) {
  showPopup('Нет избранных чатов. Добавьте их в разделе "Управление избранными чатами".');
  showFavoriteChatManager();
    return;
  }
  
favoriteChats.sort((a, b) => a.order - b.order);

const container = document.createElement('div');
container.id = 'favorite-chats-notes-ui';
container.style.position = 'fixed';
container.style.top = '30px';
container.style.left = '0';
container.style.right = '0';
container.style.padding = '16px';
container.style.display = 'flex';
container.style.flexDirection = 'column';
container.style.alignItems = 'center';
container.style.gap = '15px';
container.style.justifyContent = 'center';
container.style.zIndex = '99999';
container.style.opacity = '0'; 
container.style.transform = 'translateY(-20px)'; 
// Removed transition animation
container.style.transition = 'none';
container.style.width = '100%';
container.style.backdropFilter = 'none';
container.style.backgroundColor = 'transparent';

container.style.width = '100%';
container.style.backdropFilter = 'none';
container.style.backgroundColor = 'transparent';

// Записываем время создания UI и устанавливаем временный флаг "grace"
container.setAttribute('data-creation-time', String(creationTime));
container.setAttribute('data-grace', 'true');
// Увеличиваем время grace до 2000 мс
setTimeout(() => {
  container.removeAttribute('data-grace');
}, 2000);

// Create the main wrapper for all row containers
const rowsWrapper = document.createElement('div');
rowsWrapper.id = 'favorite-rows-wrapper';
rowsWrapper.style.display = 'flex';
rowsWrapper.style.flexDirection = 'column';
rowsWrapper.style.gap = '15px';
rowsWrapper.style.width = '100%';
rowsWrapper.style.maxWidth = '1210px'; // Increased by 10px (5px on each side)
rowsWrapper.style.marginLeft = '5px';
rowsWrapper.style.marginRight = '5px';

// Function to create a row container with a header
function createRowContainer(title, isCollapsible = true, isExpanded = true) {
  const rowContainer = document.createElement('div');
  rowContainer.className = 'favorite-row-container';
  rowContainer.style.display = 'flex';
  rowContainer.style.flexDirection = 'column';
  rowContainer.style.width = '100%';
  rowContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.4)';
  rowContainer.style.backdropFilter = 'blur(10px)';
  rowContainer.style.borderRadius = '12px';
  rowContainer.style.overflow = 'hidden';
  
  // Row header with title and controls
  const rowHeader = document.createElement('div');
  rowHeader.className = 'favorite-row-header';
  rowHeader.style.display = 'flex';
  rowHeader.style.alignItems = 'center';
  rowHeader.style.padding = '10px 15px';
  rowHeader.style.borderBottom = isExpanded ? '1px solid rgba(255, 255, 255, 0.1)' : 'none';
  rowHeader.style.transition = 'all 0.3s ease';
  
  // Row title area (includes title and + button)
  const rowTitleArea = document.createElement('div');
  rowTitleArea.style.display = 'flex';
  rowTitleArea.style.alignItems = 'center';
  rowTitleArea.style.flex = '1';
  rowTitleArea.style.gap = '10px';
  
  // Row title (editable for custom rows)
  const rowTitle = document.createElement('div');
  rowTitle.className = 'favorite-row-title';
  rowTitle.innerText = title;
  rowTitle.style.fontWeight = 'bold';
  rowTitle.style.color = 'white';
  rowTitle.style.fontSize = '16px';
  rowTitleArea.appendChild(rowTitle);
  
  // Add row + button next to title
  const addToRowBtn = document.createElement('button');
  addToRowBtn.innerHTML = '+';
  addToRowBtn.title = 'Добавить карточку в этот ряд';
  addToRowBtn.style.cssText = `
    background: none;
    border: none;
color: white;
    font-size: 16px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s ease;
    padding: 0;
    margin-left: 8px;
  `;
  
  addToRowBtn.addEventListener('mouseover', () => {
    addToRowBtn.style.opacity = '1';
  });
  
  addToRowBtn.addEventListener('mouseout', () => {
    addToRowBtn.style.opacity = '0.7';
  });
  
  addToRowBtn.addEventListener('click', (e) => {
  e.stopPropagation();
    // Open the favorites manager to add a new card to this row
    const rowId = rowContainer.getAttribute('data-row-id');
    showFavoriteChatManager(rowId);
  });
  
  rowTitleArea.appendChild(addToRowBtn);
  
  // Make title editable for custom rows
  if (isCollapsible && title !== 'Избранные чаты') {
    rowTitle.style.cursor = 'text';
    rowTitle.setAttribute('contenteditable', 'true');
    rowTitle.addEventListener('blur', () => {
      // Save the title in the row's data attribute
      rowContainer.setAttribute('data-title', rowTitle.innerText);
      // Save all row titles to local storage
      saveRowsConfig();
    });
    
    rowTitle.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
      e.preventDefault();
        rowTitle.blur();
      }
    });
  }
  
  // Row controls
  const rowControls = document.createElement('div');
  rowControls.className = 'favorite-row-controls';
  rowControls.style.display = 'flex';
  rowControls.style.alignItems = 'center';
  rowControls.style.gap = '10px';
  
  // Collapse/expand toggle
  if (isCollapsible) {
    const toggleBtn = document.createElement('button');
    toggleBtn.innerHTML = isExpanded ? '▼' : '►';
    toggleBtn.title = isExpanded ? 'Свернуть' : 'Развернуть';
    toggleBtn.style.cssText = `
        background: none; 
      border: none;
        color: white; 
        font-size: 14px; 
        cursor: pointer;
      opacity: 0.7;
      transition: opacity 0.2s ease;
      padding: 0 5px;
    `;
    
    toggleBtn.addEventListener('mouseover', () => {
      toggleBtn.style.opacity = '1';
    });
    
    toggleBtn.addEventListener('mouseout', () => {
      toggleBtn.style.opacity = '0.7';
    });
    
    toggleBtn.addEventListener('click', (e) => {
        e.stopPropagation();
      
      const cardsContainer = rowContainer.querySelector('.favorite-cards-container');
      const isCurrentlyExpanded = toggleBtn.innerHTML === '▼';
      
      if (isCurrentlyExpanded) {
        // Collapse
        cardsContainer.style.display = 'none';
        toggleBtn.innerHTML = '►';
        toggleBtn.title = 'Развернуть';
        rowHeader.style.borderBottom = 'none';
    } else {
        // Expand
cardsContainer.style.display = 'flex';
        toggleBtn.innerHTML = '▼';
        toggleBtn.title = 'Свернуть';
        rowHeader.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
      }
      
      // Save the expanded state
      rowContainer.setAttribute('data-expanded', (!isCurrentlyExpanded).toString());
      saveRowsConfig();
      
      // Обновляем высоту заметок при изменении состояния строки
      updateGeneralNotesHeight();
    });
    
    rowControls.appendChild(toggleBtn);
  }
  
  rowHeader.appendChild(rowTitleArea);
  rowHeader.appendChild(rowControls);
  rowContainer.appendChild(rowHeader);
  
  // Cards container for this row
  const cardsContainer = document.createElement('div');
  cardsContainer.className = 'favorite-cards-container';
  cardsContainer.style.display = isExpanded ? 'flex' : 'none';
cardsContainer.style.gap = '15px';
cardsContainer.style.flexWrap = 'nowrap';
cardsContainer.style.maxWidth = '100%';
cardsContainer.style.overflowX = 'auto';
cardsContainer.style.padding = '10px 15px';
cardsContainer.style.scrollbarWidth = 'thin';
cardsContainer.style.scrollbarColor = 'rgba(255, 255, 255, 0.2) transparent';
cardsContainer.style.msOverflowStyle = 'none'; // IE и Edge

  // Setup drag and drop for this container
  setupContainerDragDrop(cardsContainer);
  
  rowContainer.appendChild(cardsContainer);
  
  // Set initial data attributes
  rowContainer.setAttribute('data-expanded', isExpanded.toString());
  rowContainer.setAttribute('data-title', title);
  rowContainer.setAttribute('data-row-id', title.toLowerCase().replace(/\s+/g, '-'));
  
  return rowContainer;
}

// Function to handle drag and drop for a container
function setupContainerDragDrop(container) {
  container.addEventListener('dragover', (e) => {
    e.preventDefault();
    const draggingCard = document.querySelector('.favorite-chat-card.dragging');
    if (!draggingCard) return;
    
    const afterElement = getDragAfterElement(container, e.clientX);
    if (afterElement == null) {
      container.appendChild(draggingCard);
} else {
      container.insertBefore(draggingCard, afterElement);
    }
  });
  
  container.addEventListener('drop', (e) => {
    e.preventDefault();
    // Update the row ID for the card
    const draggingCard = document.querySelector('.favorite-chat-card.dragging');
    if (draggingCard) {
      const rowContainer = container.closest('.favorite-row-container');
      if (rowContainer) {
        const rowId = rowContainer.getAttribute('data-row-id');
        const chatId = draggingCard.getAttribute('data-chat-id');
        
        // Set the data-row-id attribute on the card itself
        draggingCard.setAttribute('data-row-id', rowId);
        
        // Update the card's row assignment in localStorage
        updateCardRowAssignment(chatId, rowId);
        
        // Log for debugging
        console.log(`Card ${chatId} moved to row ${rowId}`);
      }
    }
    
    // Update the order of cards within each row
    updateChatOrder();
  });
  
  // Add dragstart event to properly set dragging class
  container.addEventListener('dragstart', (e) => {
    if (e.target.classList.contains('favorite-chat-card')) {
      e.target.classList.add('dragging');
      // Set data to ensure drag works across browsers
      e.dataTransfer.setData('text/plain', e.target.getAttribute('data-chat-id'));
      // Use move effect
      e.dataTransfer.effectAllowed = 'move';
    }
  }, true);
  
  // Add dragend event to clean up
  container.addEventListener('dragend', (e) => {
    if (e.target.classList.contains('favorite-chat-card')) {
      e.target.classList.remove('dragging');
    }
  }, true);
}

// Helper function to get element to drag after
function getDragAfterElement(container, x) {
  const draggableElements = [...container.querySelectorAll('.favorite-chat-card:not(.dragging)')];
  
  return draggableElements.reduce((closest, child) => {
    const box = child.getBoundingClientRect();
    const offset = x - box.left - box.width / 2;
    
    if (offset < 0 && offset > closest.offset) {
      return { offset: offset, element: child };
        } else {
      return closest;
    }
  }, { offset: Number.NEGATIVE_INFINITY }).element;
}

// Function to save row assignments for cards
function updateCardRowAssignment(chatId, rowId) {
  // Make sure we have the chat ID
  if (!chatId) {
    console.error('No chat ID provided for row assignment');
    return;
  }
  
  // Get current assignments
  let cardRowAssignments = JSON.parse(localStorage.getItem('card_row_assignments') || '{}');
  
  // Update or set the assignment
  cardRowAssignments[chatId] = rowId;
  
  // Save back to localStorage
  localStorage.setItem('card_row_assignments', JSON.stringify(cardRowAssignments));
  
  // Log for debugging
  console.log('Updated row assignments:', cardRowAssignments);
}

// Function to save rows configuration
function saveRowsConfig() {
  const rows = Array.from(document.querySelectorAll('.favorite-row-container'));
  const rowsConfig = rows.map(row => {
          return {
      id: row.getAttribute('data-row-id'),
      title: row.getAttribute('data-title'),
      expanded: row.getAttribute('data-expanded') === 'true'
    };
  });
  
  // Сохраняем все ряды включая "Избранные чаты"
  localStorage.setItem('all_rows_config', JSON.stringify(rowsConfig));
  
  // Дополнительно сохраняем кастомные ряды для обратной совместимости
  const customRows = rowsConfig.filter(row => row.id !== 'избранные-чаты');
  localStorage.setItem('custom_rows', JSON.stringify(customRows));
}

// Function to update chat order within rows
function updateChatOrder() {
  // Get all rows
  const rows = document.querySelectorAll('.favorite-row-container');
  const updatedFavoriteChats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
  
  // Process each row
  rows.forEach(row => {
    const cardsContainer = row.querySelector('.favorite-cards-container');
    const cards = cardsContainer.querySelectorAll('.favorite-chat-card[data-chat-id]');
    
    // Update order within this row
  cards.forEach((card, newIndex) => {
    const chatId = card.getAttribute('data-chat-id');
    const chatToUpdate = updatedFavoriteChats.find(c => c.id === chatId);
    
    if (chatToUpdate) {
      chatToUpdate.order = newIndex * 10;
    
        // Update the card number display
    const numberEl = card.querySelector('.chat-number');
    if (numberEl) {
      numberEl.innerText = `${newIndex + 1}.`;
        }
    }
    });
  });
  
  localStorage.setItem('favorite_chats', JSON.stringify(updatedFavoriteChats));
}

// Create the main row for favorite chats
// Загружаем сохраненную конфигурацию для всех рядов
const allRowsConfig = JSON.parse(localStorage.getItem('all_rows_config') || '[]');

// Находим конфигурацию для "Избранные чаты" если она есть
let mainRowExpanded = true; // По умолчанию раскрыто
const mainRowConfig = allRowsConfig.find(row => row.id === 'избранные-чаты');
if (mainRowConfig) {
  mainRowExpanded = mainRowConfig.expanded;
}

const mainRow = createRowContainer('Избранные чаты', true, mainRowExpanded); // Используем сохраненное состояние
const mainCardsContainer = mainRow.querySelector('.favorite-cards-container');

// Load saved custom rows
const savedRows = JSON.parse(localStorage.getItem('custom_rows') || '[]');
if (savedRows.length === 0) {
  // Create default custom rows if none exist
  savedRows.push({ id: 'рабочие-чаты', title: 'Рабочие чаты', expanded: true });
  savedRows.push({ id: 'личные-чаты', title: 'Личные чаты', expanded: true });
  localStorage.setItem('custom_rows', JSON.stringify(savedRows));
} else {
  // Обновляем состояние сохраненных рядов из all_rows_config если доступно
  savedRows.forEach(row => {
    const savedConfig = allRowsConfig.find(config => config.id === row.id);
    if (savedConfig) {
      row.expanded = savedConfig.expanded;
    }
  });
}

// Load card row assignments
const cardRowAssignments = JSON.parse(localStorage.getItem('card_row_assignments') || '{}');

// Add rows to the wrapper
rowsWrapper.appendChild(mainRow);

// Create custom rows
savedRows.forEach(rowConfig => {
  const customRow = createRowContainer(rowConfig.title, true, rowConfig.expanded);
  rowsWrapper.appendChild(customRow);
});

// Add the rows wrapper to the main container
container.appendChild(rowsWrapper);

// Обновляем высоту заметок после создания всех строк
setTimeout(updateGeneralNotesHeight, 100);

// Now distribute cards to their assigned rows or default to main row
favoriteChats.forEach((chat, index) => {
  const chatCard = document.createElement('div');
  chatCard.className = 'favorite-chat-card';
  chatCard.setAttribute('data-chat-id', chat.id);
  chatCard.setAttribute('data-index', index);
  chatCard.setAttribute('data-original-index', index);
  chatCard.setAttribute('draggable', 'true');
  chatCard.style.minWidth = '250px';
  chatCard.style.maxWidth = '289px';
  chatCard.style.height = '150px';
  chatCard.style.backgroundColor = '#212452';
  chatCard.style.borderRadius = '12px';
  chatCard.style.padding = '15px';
  chatCard.style.boxSizing = 'border-box';
  chatCard.style.cursor = 'grab';
  chatCard.style.position = 'relative';
  chatCard.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease';
  chatCard.style.display = 'flex';
  chatCard.style.flexDirection = 'column';
  chatCard.style.color = 'white';
  chatCard.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';
  chatCard.style.overflow = 'hidden'; // Add overflow hidden to the card
  
  chatCard.addEventListener('mouseover', () => {
    if (!chatCard.classList.contains('dragging')) {
      chatCard.style.transform = 'translateY(-5px)';
      chatCard.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.25)';
      chatCard.style.backgroundColor = '#2d3165';
      
      // Show buttons on hover
      deleteBtn.style.opacity = '0.7';
      deleteBtn.style.pointerEvents = 'auto';
      addRelatedChatBtn.style.opacity = '1'; // Make it fully visible
      addRelatedChatBtn.style.pointerEvents = 'auto';
      dragIcon.style.opacity = '0.7'; // Show drag icon
      dragIcon.style.pointerEvents = 'auto'; // Make drag icon interactive
    }
  });
  
  chatCard.addEventListener('mouseout', () => {
    if (!chatCard.classList.contains('dragging')) {
      chatCard.style.transform = 'translateY(0)';
      chatCard.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
      chatCard.style.backgroundColor = '#212452';
      
      // Hide buttons on mouseout, но оставляем видимой кнопку добавления связанных чатов
      deleteBtn.style.opacity = '0';
      deleteBtn.style.pointerEvents = 'none';
      // Кнопка добавления связанных чатов остается видимой
      addRelatedChatBtn.style.opacity = '1';
      addRelatedChatBtn.style.pointerEvents = 'auto';
      dragIcon.style.opacity = '0'; // Hide drag icon
      dragIcon.style.pointerEvents = 'none'; // Make drag icon non-interactive
    }
  });
  
  const chatHeader = document.createElement('div');
  chatHeader.style.marginBottom = '10px';
  chatHeader.style.display = 'flex';
  chatHeader.style.alignItems = 'center';
  chatHeader.style.gap = '8px';
  
  const chatNumber = document.createElement('div');
  chatNumber.className = 'chat-number';
  chatNumber.innerText = `${index + 1}.`;
  chatNumber.style.fontSize = '15px';
  chatNumber.style.color = 'rgba(255, 255, 255, 0.7)';
  
  const chatName = document.createElement('div');
  chatName.innerText = chat.name;
  chatName.style.fontWeight = 'bold';
  chatName.style.fontSize = '18px';
  chatName.style.wordBreak = 'break-word';
  chatName.style.maxHeight = '40px';
  chatName.style.overflow = 'hidden';
  chatName.style.whiteSpace = 'normal';
  chatName.style.textOverflow = 'ellipsis';
  chatName.style.display = '-webkit-box';
  chatName.style.webkitLineClamp = '2';
  chatName.style.webkitBoxOrient = 'vertical';
  
  chatHeader.appendChild(chatNumber);
  chatHeader.appendChild(chatName);
  chatCard.appendChild(chatHeader);
  
  // Add chat notes at the bottom
  const noteCookieName = "telegram_chat_note_" + chat.id;
  const chatNotes = loadNote(noteCookieName) || "";
  
  const notesPreview = document.createElement('div');
  notesPreview.style.fontSize = '13px';
  notesPreview.style.color = 'rgba(255, 255, 255, 0.7)';
  notesPreview.style.lineHeight = '1.2';
  notesPreview.style.overflow = 'auto';
  notesPreview.style.flex = '1';
  notesPreview.style.maxHeight = '70px';
  notesPreview.style.display = 'block';
  notesPreview.style.wordBreak = 'break-word';
  notesPreview.style.paddingRight = '5px';
  notesPreview.style.scrollbarWidth = 'thin';
  notesPreview.style.scrollbarColor = 'rgba(255, 255, 255, 0.3) transparent';
  notesPreview.style.whiteSpace = 'pre-wrap'; // Add this to preserve line breaks
  
  if (chatNotes.trim() === '') {
    notesPreview.innerHTML = '<i style="opacity: 0.6;">Нет заметок</i>';
  } else {
    // Replace newlines with <br> tags and preserve other HTML
    notesPreview.innerHTML = chatNotes
      .split('\n')
      .map(line => {
        // Process links in each line
        return line.replace(
          /((?:https?:\/\/)?(?:www\.)?(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(?:\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)?)/g,
          (match) => {
            const fullURL = /^https?:\/\//.test(match) ? match : "https://" + match;
            return `<a href="${fullURL}" target="_blank" style="color: #4b6cb7; text-decoration: none; cursor: pointer;" title="${match}">${shortenUrl(match)}</a>`;
          }
        );
      })
      .join('<br>');
  }
  
  // Container for related chats
  const relatedChatsContainer = document.createElement('div');
  relatedChatsContainer.className = 'related-chats-container';
  relatedChatsContainer.style.cssText = `
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 5px;
    min-height: 24px;
  `;

  // Load related chats
  const relatedChats = loadRelatedChats(chat.id);
  
  // Create mini buttons for each related chat
  relatedChats.forEach(relatedChat => {
    const miniButton = document.createElement('div');
    miniButton.className = 'related-chat-button';
    miniButton.title = relatedChat.name;
    miniButton.style.cssText = `
      padding: 3px 10px;
      background-color: rgba(75, 108, 183, 0.3);
      border-radius: 4px;
      color: white;
      font-size: 12px;
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 120px;
      display: flex;
      align-items: center;
    `;
    miniButton.innerHTML = `
      <span style="overflow: hidden; text-overflow: ellipsis;">${relatedChat.name}</span>
    `;
    
    // Add click handler to open the related chat
    miniButton.addEventListener('click', (e) => {
      e.stopPropagation(); // Prevent triggering the parent card's click
      
      try {
        // Get the chat id
        const chatId = relatedChat.id;
        
        // For K version
        // Emulate how Telegram web K normally navigates between chats
        if (window.location.href.includes('web.telegram.org/k')) {
          // Navigate as K version does
          window.location.href = `https://web.telegram.org/k/#${chatId}`;
        } else {
          // For any other version
          window.location.hash = chatId;
        }
        
        // Close the UI with animation
        const uiContainer = document.getElementById('favorite-chats-notes-ui');
        if (uiContainer) {
          uiContainer.style.opacity = '0';
          uiContainer.style.transform = 'translateY(-20px)';
          setTimeout(() => {
            if (document.body.contains(uiContainer)) {
              uiContainer.remove();
            }
            ensureInputUnblocked();
          }, 300);
        }
      } catch (error) {
        console.error('Error navigating to chat:', error);
        // Simple fallback
        window.location.hash = chatId;
      }
    });
    
    // Add delete button when hovering
    miniButton.addEventListener('mouseenter', () => {
      const deleteBtn = document.createElement('span');
      deleteBtn.className = 'delete-related-chat';
      deleteBtn.innerHTML = '×';
      deleteBtn.style.cssText = `
        margin-left: 4px;
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
        font-weight: bold;
      `;
      
      deleteBtn.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent navigation
        
        // Запрашиваем подтверждение на удаление
        if (confirm(`Вы уверены, что хотите удалить связанный чат "${relatedChat.name}"?`)) {
          // Remove the related chat from the list
          const updatedRelatedChats = loadRelatedChats(chat.id).filter(rc => rc.id !== relatedChat.id);
          saveRelatedChats(chat.id, updatedRelatedChats);
          
          // Remove the button from UI
          miniButton.remove();
          
          // Показываем два уведомления: сначала алерт, затем всплывающее уведомление
          alert(`Связанный чат "${relatedChat.name}" удален`);
          showPopup('Связанный чат удален');
        }
      });
      
      miniButton.appendChild(deleteBtn);
    });
    
    // Remove delete button when not hovering
    miniButton.addEventListener('mouseleave', () => {
      const deleteBtn = miniButton.querySelector('.delete-related-chat');
      if (deleteBtn) deleteBtn.remove();
    });
    
    relatedChatsContainer.appendChild(miniButton);
  });
  
  // Add button
  const addRelatedChatBtn = document.createElement('div');
  addRelatedChatBtn.className = 'add-related-chat-button';
  addRelatedChatBtn.innerHTML = '+';
  addRelatedChatBtn.title = 'Добавить связанный чат';
  addRelatedChatBtn.style.cssText = `
    padding: 3px 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: white;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s ease; /* Smooth transition */
  `;
  
  // Add click handler to open the related chat picker
  addRelatedChatBtn.addEventListener('click', (e) => {
    e.stopPropagation(); // Prevent triggering the parent card's click
    
    // Show a modal to add a related chat
    showRelatedChatPicker(chat.id, relatedChatsContainer);
  });
  
  relatedChatsContainer.appendChild(addRelatedChatBtn);
  
  // Add all elements to card
  chatCard.appendChild(chatHeader);
  chatCard.appendChild(notesPreview);
  chatCard.appendChild(relatedChatsContainer);
  
  const dragIcon = document.createElement('div');
  dragIcon.style.position = 'absolute';
  dragIcon.style.top = '10px';
  dragIcon.style.right = '10px';
  dragIcon.style.fontSize = '16px';
  dragIcon.style.opacity = '0'; // Initially hidden
  dragIcon.style.pointerEvents = 'none'; // Initially not interactive
  dragIcon.style.cursor = 'grab';
  dragIcon.innerHTML = '⋮⋮';
  dragIcon.title = 'Перетащите для изменения порядка';
  dragIcon.style.transition = 'opacity 0.2s ease'; // Smooth transition
  
  // Removed individual hover listeners for dragIcon
  
  chatCard.appendChild(dragIcon);
  
  const deleteBtn = document.createElement('button');
  deleteBtn.innerHTML = '🗑️';
  deleteBtn.title = 'Удалить чат из избранных';
  deleteBtn.style.position = 'absolute';
  deleteBtn.style.top = '10px';
  deleteBtn.style.right = '40px';
  deleteBtn.style.fontSize = '16px';
  deleteBtn.style.opacity = '0'; // Initially hidden
  deleteBtn.style.pointerEvents = 'none'; // Initially not interactive
  deleteBtn.style.cursor = 'pointer';
  deleteBtn.style.background = 'transparent';
  deleteBtn.style.border = 'none';
  deleteBtn.style.transition = 'opacity 0.2s ease'; // Smooth transition
  
  deleteBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    if (confirm(`Удалить чат "${chat.name}" из избранных?`)) {
      let favorites = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
      favorites = favorites.filter(item => item.id !== chat.id);
      localStorage.setItem('favorite_chats', JSON.stringify(favorites));
      chatCard.remove();
      updateChatOrder();
      showPopup(`Чат "${chat.name}" удален из избранных.`);
    }
  });
  chatCard.appendChild(deleteBtn);
  
  chatCard.addEventListener('click', (e) => {
    // If clicking on any button (delete, close, save)
    if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
      e.stopPropagation();
      return;
    }

    if (e.target === dragIcon || dragIcon.contains(e.target)) {
      return;
    }

    // Add Command+click handler for editing notes or name
    if (e.metaKey || e.ctrlKey) {
      e.preventDefault();
      e.stopPropagation();

      // If clicking on the chat name, edit the name instead of notes
      if (e.target === chatName || chatName.contains(e.target)) {
        // Make chat name editable
        chatName.contentEditable = 'true';
        chatName.style.cursor = 'text';
        chatName.focus();

        // Save original name in case of cancel
        const originalName = chatName.innerText;

        // Handle Enter key to save
        chatName.addEventListener('keydown', function nameEditHandler(e) {
          if (e.key === 'Enter') {
            e.preventDefault();
            chatName.blur();
          }
          if (e.key === 'Escape') {
            chatName.innerText = originalName;
            chatName.blur();
          }
        });

        // Handle blur to save changes
        chatName.addEventListener('blur', function saveNameHandler() {
          chatName.contentEditable = 'false';
          chatName.style.cursor = '';
          
          const newName = chatName.innerText.trim();
          if (newName && newName !== originalName) {
            // Update name in localStorage
            let favorites = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
            const chatToUpdate = favorites.find(c => c.id === chat.id);
            if (chatToUpdate) {
              chatToUpdate.name = newName;
              localStorage.setItem('favorite_chats', JSON.stringify(favorites));
              showPopup('Название чата обновлено');
            }
          } else if (!newName) {
            chatName.innerText = originalName;
          }

          // Remove event listeners
          chatName.removeEventListener('keydown', nameEditHandler);
          chatName.removeEventListener('blur', saveNameHandler);
        });

        return;
      }
      
      // Close any other open editing cards first
      const otherEditingCards = document.querySelectorAll('.favorite-chat-card.editing');
      otherEditingCards.forEach(card => {
        if (card !== chatCard) {
          // Find the close button specifically
          const closeBtn = card.querySelector('button[title="Закрыть редактирование"]');
          if (closeBtn) {
            closeBtn.click();
          } else {
            // Fallback: try to find any save button
            const saveBtn = Array.from(card.querySelectorAll('button')).find(btn => 
              btn.textContent === 'Закрыть');
            if (saveBtn) {
              saveBtn.click();
            } else {
              // Force remove editing class if buttons not found
              card.classList.remove('editing');
              // Restore default styles
              card.style.height = '150px';
              card.style.minHeight = '150px';
              card.style.minWidth = '220px';
              card.style.zIndex = '';
              card.style.boxShadow = '';
              card.style.backgroundColor = '#212452';
              // Show preview again
              const preview = card.querySelector('div[style*="overflow: auto"]');
              if (preview) preview.style.display = 'block';
              // Remove any edit fields and buttons
              card.querySelectorAll('.notes-edit-field, button').forEach(el => {
                if (!el.title || el.title !== 'Удалить чат из избранных') {
                  el.remove();
                }
              });
            }
          }
        }
      });
      
      // Add editing class to card
      chatCard.classList.add('editing');
      
      // Block only Telegram input while editing
      blockTelegramInput();
      
      // Save original height for animation
      const originalHeight = chatCard.offsetHeight;
      const originalWidth = chatCard.offsetWidth;
      
      // Modify card styles for editing mode
      chatCard.style.height = 'auto';
      chatCard.style.minHeight = '300px';
      chatCard.style.minWidth = '500px'; // Increased from 400px (25% wider)
      chatCard.style.transition = 'all 0.3s ease';
      chatCard.style.zIndex = '1000';
      chatCard.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.3)';
      chatCard.style.backgroundColor = '#2d3165';
      
      // Hide preview temporarily
      notesPreview.style.display = 'none';
      
      // Create edit field
      const editField = document.createElement('div');
      editField.contentEditable = 'true';
      editField.className = 'notes-edit-field';
      editField.style.cssText = `
        width: 100%;
        height: 350px;
        max-height: 400px;
        padding: 15px;
        background-color: #1e1e1e;
        border: 2px solid #4b6cb7;
        border-radius: 4px;
        color: #fff;
        font-size: 14px;
        line-height: 1.4;
        overflow-y: auto;
        white-space: pre-wrap;
        word-wrap: break-word;
        margin: 10px 0;
        outline: none;
        transition: border-color 0.2s ease;
        max-width: 100%;
        resize: none;
      `;

      // Предотвращаем всплытие событий только для поля редактирования
      editField.addEventListener('click', (e) => {
        // Если это ссылка, разрешаем открытие по клику
        if (e.target.tagName === 'A') {
          // Предотвращаем редактирование ссылки
          e.preventDefault();
          
          // Открываем ссылку в новой вкладке
          window.open(e.target.href, '_blank');
          return;
        }
        
        // Для всех остальных элементов предотвращаем всплытие
        e.stopPropagation();
      });

      editField.addEventListener('keydown', (e) => {
        e.stopPropagation();
      });

      editField.addEventListener('input', (e) => {
        e.stopPropagation();
      });

      // Load existing note content
      const noteCookieName = "telegram_chat_note_" + chat.id;
      const savedNote = loadNote(noteCookieName) || "";
      editField.innerHTML = savedNote
        .split('\n')
        .map(line => {
          return line.replace(
            /((?:https?:\/\/)?(?:www\.)?(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(?:\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=]*)?)/g,
            (match) => {
              const fullURL = /^https?:\/\//.test(match) ? match : "https://" + match;
              return `<a href="${fullURL}" target="_blank" style="color: #4b6cb7; text-decoration: none; cursor: pointer;" title="${match}">${shortenUrl(match)}</a>`;
            }
          );
        })
        .join('<br>');

      // Create close button
      const closeButton = document.createElement('button');
      closeButton.innerHTML = '✕';
      closeButton.style.cssText = `
        position: absolute;
        top: 10px;
        right: 10px;
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        padding: 5px;
        z-index: 1001;
        opacity: 0.7;
        transition: opacity 0.2s ease;
      `;
      closeButton.title = 'Закрыть редактирование';

      closeButton.addEventListener('mouseover', () => {
        closeButton.style.opacity = '1';
      });

      closeButton.addEventListener('mouseout', () => {
        closeButton.style.opacity = '0.7';
      });

      // Create save button
      const saveButton = document.createElement('button');
      saveButton.textContent = 'Закрыть';
      saveButton.style.cssText = `
        width: 100%;
        padding: 8px;
        background-color: #4b6cb7;
        border: none;
        border-radius: 4px;
        color: white;
        cursor: pointer;
        margin-top: 10px;
        transition: background-color 0.2s ease;
      `;

      saveButton.addEventListener('mouseover', () => {
        saveButton.style.backgroundColor = '#3a539b';
      });

      saveButton.addEventListener('mouseout', () => {
        saveButton.style.backgroundColor = '#4b6cb7';
      });

      function exitEditMode(e) {
        if (e) {
          e.stopPropagation(); // Ensure event doesn't propagate
        }
        
        // Unblock input when exiting edit mode
        ensureInputUnblocked();
        
        // Save content
        const tempDiv = editField.cloneNode(true);
        const linkMap = new Map();
        let index = 0;
        
        tempDiv.querySelectorAll('a').forEach(a => {
          const placeholder = `__LINK_PLACEHOLDER_${index}__`;
          linkMap.set(placeholder, a.getAttribute('href'));
          a.replaceWith(placeholder);
          index++;
        });
        
        let fullText = tempDiv.innerHTML
          .replace(/<br>/g, '\n')
          .replace(/<div>/g, '\n')
          .replace(/<\/div>/g, '')
          .replace(/&nbsp;/g, ' ');
        
        linkMap.forEach((href, placeholder) => {
          fullText = fullText.replace(placeholder, href);
        });
        
        saveNote(noteCookieName, fullText);
        
        // Restore card
        chatCard.style.height = originalHeight + 'px';
        chatCard.style.minHeight = originalHeight + 'px';
        chatCard.style.minWidth = originalWidth + 'px';
        chatCard.style.zIndex = '';
        chatCard.style.boxShadow = '';
        chatCard.style.backgroundColor = '#212452';
        
        // Update preview
        notesPreview.style.display = 'block';
        notesPreview.innerHTML = fullText || 'Нет заметок';
        if (!fullText) {
          notesPreview.style.fontStyle = 'italic';
          notesPreview.style.opacity = '0.6';
        } else {
          notesPreview.style.fontStyle = 'normal';
          notesPreview.style.opacity = '1';
        }
        
        // Remove edit elements
        editField.remove();
        closeButton.remove();
        saveButton.remove();
        
        // Remove editing class
        chatCard.classList.remove('editing');
        
        showPopup('Заметки сохранены');
      }

      closeButton.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent click from bubbling to the card
        exitEditMode(e);
      });
      saveButton.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent click from bubbling to the card
        exitEditMode(e);
      });

      // Add elements to card
      chatCard.appendChild(closeButton);
      chatCard.appendChild(editField);
      chatCard.appendChild(saveButton);
      
      // Focus edit field
      editField.focus();

      return;
    }

    // Only proceed with navigation if not in editing mode
    if (!chatCard.classList.contains('editing')) {
      window.location.hash = chat.id;
    }
  });
  
  chatCard.addEventListener('dragstart', (e) => {
    e.dataTransfer.setData('text/plain', index);
    setTimeout(() => {
      chatCard.classList.add('dragging');
      chatCard.style.opacity = '0.7';
    }, 0);
  });
  
  chatCard.addEventListener('dragend', (e) => {
    chatCard.classList.remove('dragging');
    chatCard.style.opacity = '1';
    
    // Get the container this card was dropped into
    const container = chatCard.parentElement;
    if (container && container.classList.contains('favorite-cards-container')) {
      // Find the row container to get its ID
      const rowContainer = container.closest('.favorite-row-container');
      if (rowContainer) {
        const rowId = rowContainer.getAttribute('data-row-id');
        const chatId = chatCard.getAttribute('data-chat-id');
        
        // Update the card's data attribute
        chatCard.setAttribute('data-row-id', rowId);
        
        // Save the row assignment
        updateCardRowAssignment(chatId, rowId);
        
        console.log(`Card ${chatId} dropped into row ${rowId} (from dragend event)`);
      }
    }
    
    // Update card order in all rows
    updateChatOrder();
  });
  
  // First check if this card has a saved row assignment
  const rowId = cardRowAssignments[chat.id];
  let targetContainer = mainCardsContainer; // Default to main row
  let targetRowFound = false;
  
  if (rowId) {
    // Find the assigned row
    const targetRow = document.querySelector(`.favorite-row-container[data-row-id="${rowId}"]`);
    if (targetRow) {
      targetContainer = targetRow.querySelector('.favorite-cards-container');
      chatCard.setAttribute('data-row-id', rowId);
      targetRowFound = true;
      
      // Log successful assignment
      console.log(`Card ${chat.id} placed in saved row: ${rowId}`);
  } else {
      // Log that the saved row wasn't found
      console.warn(`Saved row ${rowId} for card ${chat.id} not found, using default row`);
    }
  }
  
  // If no saved assignment or target row not found, set to main row
  if (!targetRowFound) {
    chatCard.setAttribute('data-row-id', 'избранные-чаты');
    console.log(`Card ${chat.id} placed in default row (no saved assignment)`);
  }
  
  // Add the card to the target container
  targetContainer.appendChild(chatCard);
});

// Add a function to re-initialize card row assignments from localStorage
function refreshCardRowAssignments() {
  // Get all cards
  const allCards = document.querySelectorAll('.favorite-chat-card[data-chat-id]');
  
  // Get current assignments
  const cardRowAssignments = JSON.parse(localStorage.getItem('card_row_assignments') || '{}');
  
  console.log('Refreshing card row assignments:', cardRowAssignments);
  
  // Update each card's position
  allCards.forEach(card => {
    const chatId = card.getAttribute('data-chat-id');
    const rowId = cardRowAssignments[chatId];
    
    if (rowId) {
      // Find the row container
      const targetRow = document.querySelector(`.favorite-row-container[data-row-id="${rowId}"]`);
      if (targetRow) {
        // Move card to the correct row
        const targetContainer = targetRow.querySelector('.favorite-cards-container');
        if (targetContainer) {
          // Only move if it's not already in the right container
          const currentRow = card.closest('.favorite-row-container');
          if (currentRow && currentRow.getAttribute('data-row-id') !== rowId) {
            targetContainer.appendChild(card);
            card.setAttribute('data-row-id', rowId);
            console.log(`Moved card ${chatId} to row ${rowId} during refresh`);
          }
        }
      }
    }
  });
  
  // Update the order within each row
  updateChatOrder();
}

// Add manageButton to the main row
const manageButton = document.createElement('div');
manageButton.className = 'favorite-chat-card';
manageButton.style.minWidth = '50px';
manageButton.style.height = '150px';
manageButton.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
manageButton.style.borderRadius = '12px';
manageButton.style.display = 'flex';
manageButton.style.alignItems = 'center';
manageButton.style.justifyContent = 'center';
manageButton.style.cursor = 'pointer';
manageButton.style.fontSize = '24px';
manageButton.style.transition = 'background-color 0.2s ease';
manageButton.innerHTML = '+';

manageButton.addEventListener('mouseover', () => {
  manageButton.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
});

manageButton.addEventListener('mouseout', () => {
  manageButton.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
});

manageButton.addEventListener('click', () => {
  container.style.opacity = '0';
  container.style.transform = 'translateY(-20px)';
  
  setTimeout(() => {
    container.remove();
    showFavoriteChatManager();
  }, 300);
});

mainCardsContainer.appendChild(manageButton);

// Предотвращаем всплытие событий для контейнера
container.addEventListener('mousedown', (e) => {
  // Only stop propagation if not clicking on an input or textarea
  if (!e.target.matches('input, textarea')) {
    e.stopPropagation();
  }
});

container.addEventListener('click', (e) => {
  // Only stop propagation if not clicking on an input or textarea
  if (!e.target.matches('input, textarea')) {
    e.stopPropagation();
  }
});

// Добавляем контейнер в DOM
document.body.appendChild(container);

// Показываем UI
// Set immediately without animation 
container.style.opacity = '1';
container.style.transform = 'none';

// Make sure cards are in their correct rows
setTimeout(() => refreshCardRowAssignments(), 100);

// Create the general notes container
const generalNotesContainer = document.createElement('div');
generalNotesContainer.id = 'general-notes-container';
generalNotesContainer.style.cssText = `
  position: relative;
  width: 40%;
  max-width: 45%;
  background-color: #1a1d40;
  border-radius: 12px;
  padding: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  margin-top: 5px;
  margin-bottom: 5px;
  display: flex;
  flex-direction: column;
`;

// Create the general notes header
const generalNotesHeader = document.createElement('div');
generalNotesHeader.style.cssText = `
  display: flex; /* Use flexbox for layout */
  align-items: baseline; /* Align items by their text baseline */
  gap: 10px; /* Add gap between button and tabs */
  margin-bottom: 10px;
  color: white;
`;
generalNotesHeader.textContent = ''; // Removed "Общие заметки" text
generalNotesContainer.appendChild(generalNotesHeader);

// Create tabs container
const tabsContainer = document.createElement('div');
tabsContainer.style.cssText = `
  display: flex;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  margin-bottom: 10px;
  padding: 2px;
  width: 80%; /* Set width to 80% of container */
  /* Removed auto margins for left alignment */
`;

// Tab data
const tabs = [
  { id: 'main', name: 'Main' },
  { id: 'backlog', name: 'Backlog' },
  { id: 'routine', name: 'Routine' },
  { id: 'mini-projects', name: 'Mini Projects' }
];

// Current active tab - Load from localStorage or default to 'main'
let activeTab = localStorage.getItem('telegram_last_active_tab') || 'main';

// If calendar is the active tab on load, we need to ensure it's properly displayed
const isCalendarActive = activeTab === 'calendar';

// Function to calculate the available height for content panes
function calculateContentHeight() {
  const collapsedRows = document.querySelectorAll('.favorite-row-container[data-expanded="false"]');
  // Use viewport height for base, clamped between min/max pixels
  let baseHeightVh = 25; // Base height as 25% of viewport height
  let calculatedBaseHeight = window.innerHeight * (baseHeightVh / 100);
  // Clamp the base height to reasonable pixel values
  let baseHeightPx = Math.max(150, Math.min(calculatedBaseHeight, 300)); 

  // Adjust height based on collapsed rows (keep this logic)
  let addedHeight = collapsedRows.length * 50; // Reduced added height per collapsed row
  let newHeight = baseHeightPx + addedHeight;
  
  return `${newHeight}px`;
}

// --- Create Standalone Calendar Button START ---
const calendarButton = document.createElement('div');
calendarButton.setAttribute('data-tab', 'calendar');
calendarButton.textContent = '📅'; // Use emoji as content
calendarButton.title = 'Calendar'; // Tooltip
calendarButton.style.cssText = `
  padding: 4px 8px; /* Revert to original padding */
  font-size: 12px; /* Match tab font size */
  height: 22px; /* Explicit height to match tabs */
  box-sizing: border-box; /* Ensure padding is included in height */
  color: ${activeTab === 'calendar' ? '#fff' : 'rgba(255, 255, 255, 0.6)'}; /* Initial state */
  background-color: ${activeTab === 'calendar' ? 'rgba(75, 108, 183, 0.3)' : 'rgba(0, 0, 0, 0.2)'}; /* Match tab style */
  cursor: pointer;
  text-align: center;
  transition: all 0.2s ease;
  border-radius: 6px; /* Match tab radius */
  margin: 1px; /* Match tab margin */
  flex-shrink: 0; /* Prevent shrinking */
  display: flex;
  align-items: center; /* Center emoji vertically */
  justify-content: center; /* Center emoji horizontally */
  line-height: 1; /* Reset line-height */
`;

// Add hover effect similar to tabs
calendarButton.addEventListener('mouseover', () => {
  if (activeTab !== 'calendar') {
    calendarButton.style.backgroundColor = 'rgba(75, 108, 183, 0.1)';
  }
});

calendarButton.addEventListener('mouseout', () => {
  if (activeTab !== 'calendar') {
    calendarButton.style.backgroundColor = 'rgba(0, 0, 0, 0.2)'; // Restore non-active background
  }
});

// Add click listener to switch tab content
calendarButton.addEventListener('click', () => {
  console.log('Calendar button clicked!');
  // Manually handle visual state for this button
  document.querySelectorAll('[data-tab]').forEach(t => {
    if (t.getAttribute('data-tab') === 'calendar') {
      t.style.color = '#fff';
      t.style.backgroundColor = 'rgba(75, 108, 183, 0.3)';
    } else {
      // Reset other tabs/buttons to non-active state
      t.style.color = 'rgba(255, 255, 255, 0.6)';
      t.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
      if (t.classList.contains('general-notes-tab')) {
        t.style.backgroundColor = 'transparent';
      }
    }
  });
  
  // Manually implement the tab content switching for calendar
  // Hide all content panes
  document.querySelectorAll('.general-notes-edit-field, .calendar-widget-container').forEach(pane => {
    pane.style.display = 'none';
  });
  
  // Calculate the height for the content pane
  const contentHeight = calculateContentHeight();
  
  // Show calendar widget
  const calendarWidget = generalNotesContainer.querySelector('.calendar-widget-container');
  if (calendarWidget) {
    calendarWidget.style.display = 'block';
    calendarWidget.style.height = contentHeight;
    displayCalendarWidget(); // Generate calendar content
  }
  
  // Update active tab
  activeTab = 'calendar';
  // Save the active tab immediately when it's changed
  localStorage.setItem('telegram_last_active_tab', activeTab);
});

// Append the button to the header
generalNotesHeader.appendChild(calendarButton);
// --- Create Standalone Calendar Button END ---

// Append the tabs container TO THE HEADER
generalNotesHeader.appendChild(tabsContainer);
// --- Standalone Calendar Button & Tabs Setup END ---

// Now add the complete header (button + tabs) to the main container
generalNotesContainer.appendChild(generalNotesHeader);

// Create tab elements and add them to the tabsContainer
tabs.forEach(tab => {
  const tabElement = document.createElement('div');
  tabElement.classList.add('general-notes-tab'); // Add class to identify regular tabs
  tabElement.setAttribute('data-tab', tab.id);
  tabElement.style.cssText = `
    padding: 4px 8px;
    font-size: 12px;
    color: ${tab.id === activeTab ? '#fff' : 'rgba(255, 255, 255, 0.6)'};
    background-color: ${tab.id === activeTab ? 'rgba(75, 108, 183, 0.3)' : 'transparent'};
    cursor: pointer;
    text-align: center;
    transition: all 0.2s ease;
    border-radius: 4px;
    margin: 1px;
    flex: 1; /* Make tabs take equal space */
    min-width: 80px; /* Set minimum width for tabs */
  `;
  tabElement.textContent = tab.name;
  
  tabElement.addEventListener('mouseover', () => {
    if (tab.id !== activeTab) {
      tabElement.style.backgroundColor = 'rgba(75, 108, 183, 0.1)';
    }
  });
  
  tabElement.addEventListener('mouseout', () => {
    if (tab.id !== activeTab) {
      tabElement.style.backgroundColor = 'transparent';
    }
  });
  
  tabElement.addEventListener('click', () => {
    // Save current tab content if it's a notes tab
    if (activeTab !== 'calendar') {
    const currentEditField = generalNotesContainer.querySelector('.general-notes-edit-field[data-tab="' + activeTab + '"]');
    if (currentEditField) {
      saveGeneralNotes(currentEditField.innerHTML, activeTab);
      }
    }
    
    // Update tabs appearance (including the standalone calendar button)
    document.querySelectorAll('[data-tab]').forEach(t => {
      if (t.getAttribute('data-tab') === tab.id) {
        t.style.color = '#fff';
        t.style.backgroundColor = 'rgba(75, 108, 183, 0.3)';
      } else {
        t.style.color = 'rgba(255, 255, 255, 0.6)';
        t.style.backgroundColor = 'transparent';
      }
    });
    
    // Hide all content panes initially
    document.querySelectorAll('.general-notes-edit-field, .calendar-widget-container').forEach(pane => {
      pane.style.display = 'none';
    });

    // Calculate the height for the content pane
    const contentHeight = calculateContentHeight();

    // Show/Hide content based on selected tab and set height
    if (tab.id === 'calendar') {
      if (calendarWidget) {
        calendarWidget.style.display = 'block';
        calendarWidget.style.height = contentHeight;
        displayCalendarWidget(); // Generate calendar content
      }
    } else { // It's a notes tab
      const targetEditField = generalNotesContainer.querySelector(`.general-notes-edit-field[data-tab="${tab.id}"]`);
      if (targetEditField) {
        targetEditField.style.display = 'block';
        targetEditField.style.height = contentHeight;
        // Ensure consistent styling (might be redundant but safe)
        targetEditField.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
        targetEditField.style.color = 'white';
        targetEditField.style.border = '1px solid rgba(255, 255, 255, 0.2)';
      }
    }
    // Update active tab
    activeTab = tab.id;
    // Save the active tab immediately when it's changed
    localStorage.setItem('telegram_last_active_tab', activeTab);
  });
  
  tabsContainer.appendChild(tabElement);
});

// Create edit fields for each tab
tabs.forEach(tab => {
  // Create edit field
  const generalNotesEditField = document.createElement('div');
  generalNotesEditField.className = 'general-notes-edit-field';
  generalNotesEditField.setAttribute('data-tab', tab.id);
  generalNotesEditField.contentEditable = 'true';
  generalNotesEditField.style.cssText = `
    width: 100%;
    height: 200px;
    padding: 10px;
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: white;
    font-size: 14px;
    line-height: 1.4;
    overflow-y: auto;
    display: ${tab.id === activeTab ? 'block' : 'none'};
    outline: none;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  `;
  
  // Add event listener for Enter key
  generalNotesEditField.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      document.execCommand('insertLineBreak');
    }
  });
  
  // Load existing notes for this tab
  const savedNotes = loadGeneralNotes(tab.id);
  generalNotesEditField.innerHTML = savedNotes;
  
  // Auto-save on input
  generalNotesEditField.addEventListener('input', () => {
    saveGeneralNotes(generalNotesEditField.innerHTML, tab.id);
  });
  
  // Prevent click propagation
  generalNotesEditField.addEventListener('click', (e) => {
    // Если это ссылка, разрешаем открытие по клику
    if (e.target.tagName === 'A') {
      // Предотвращаем редактирование ссылки
      e.preventDefault();
      
      // Открываем ссылку в новой вкладке
      window.open(e.target.href, '_blank');
      return;
    }
    
    e.stopPropagation();
  });
  
  generalNotesContainer.appendChild(generalNotesEditField);
});

// Create calendar container (will be populated when tab is clicked)
const calendarWidgetContainer = document.createElement('div');
calendarWidgetContainer.className = 'calendar-widget-container';
calendarWidgetContainer.setAttribute('data-tab', 'calendar');
calendarWidgetContainer.style.cssText = `
  width: 100%;
  /* Height is now set dynamically */
  padding: 0; /* Padding is inside the content div */
  box-sizing: border-box;
  background-color: rgb(0, 0, 0); 
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  display: ${activeTab === 'calendar' ? 'block' : 'none'}; /* Hide initially if not active */
  outline: none;
  overflow: hidden; /* Hide overflow */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  max-height: 70vh; /* Ensure it doesn't get too tall on large screens */
  min-height: 300px; /* Ensure it's not too small on small screens */
`;
// Set initial height
calendarWidgetContainer.style.height = calculateContentHeight();

// Make sure to initialize the calendar if it's the active tab on load
if (isCalendarActive) {
  setTimeout(() => {
    displayCalendarWidget();
    console.log('Calendar initialized as active tab on load');
  }, 100);
}

generalNotesContainer.appendChild(calendarWidgetContainer);

container.appendChild(generalNotesContainer);

// Add close button to general notes
const closeGeneralNotesBtn = document.createElement('button');
closeGeneralNotesBtn.innerHTML = '✕';
closeGeneralNotesBtn.style.cssText = `
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 0, 0, 0.3);
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 5px 8px;
  border-radius: 4px;
  opacity: 0.7;
  transition: all 0.2s ease;
  z-index: 100;
`;
closeGeneralNotesBtn.title = 'Закрыть общие заметки';

closeGeneralNotesBtn.addEventListener('mouseover', () => {
  closeGeneralNotesBtn.style.opacity = '1';
  closeGeneralNotesBtn.style.background = 'rgba(255, 0, 0, 0.5)';
});

closeGeneralNotesBtn.addEventListener('mouseout', () => {
  closeGeneralNotesBtn.style.opacity = '0.7';
  closeGeneralNotesBtn.style.background = 'rgba(255, 0, 0, 0.3)';
});

closeGeneralNotesBtn.addEventListener('click', () => {
  const uiContainer = document.getElementById('favorite-chats-notes-ui');
  if (uiContainer) {
    // Save the active tab before closing
    localStorage.setItem('telegram_last_active_tab', activeTab);
    
    if (document.body.contains(uiContainer)) {
      uiContainer.remove();
    }
    ensureInputUnblocked();
  }
});

// Прикрепляем к контейнеру вместо header
generalNotesContainer.appendChild(closeGeneralNotesBtn);

// Обновляем высоту полей заметок в зависимости от состояния рядов
setTimeout(updateGeneralNotesHeight, 100);
}

// Add cleanup function to ensure input blockers are removed (more targeted approach)
function ensureInputUnblocked() {
// No need to restore global handlers, we're not overriding them anymore
window.telegramInputBlocked = false;
}

// More targeted approach to block only Telegram input
function blockTelegramInput() {
window.telegramInputBlocked = true;

// Find and maybe disable the Telegram input element
const telegramInput = document.querySelector('.input-message-input, .input-message-container');
if (telegramInput) {
  // Mark it rather than disable it (disabling might cause issues)
  telegramInput.setAttribute('data-notes-editing', 'true');
}
}

// Set up interval to periodically check if UI exists and clean up if needed
setInterval(() => {
const uiContainer = document.getElementById('favorite-chats-notes-ui');
const editingCards = document.querySelectorAll('.favorite-chat-card.editing');

if (!uiContainer && window.telegramInputBlocked && editingCards.length === 0) {
  console.log("UI lost but input still marked as blocked, cleaning up...");
  ensureInputUnblocked();
  
  // Clear any input element markers
  document.querySelectorAll('[data-notes-editing="true"]').forEach(el => {
    el.removeAttribute('data-notes-editing');
  });
}
}, 1000); // Check every second

// Modify the existing event handlers for showing UI
document.addEventListener("keydown", (e) => {
const activeEl = document.activeElement;
if (activeEl && (activeEl.id === "grammarPromptTextarea" || activeEl.id === "systemPromptTextarea")) {
  return;
}

const shortcutFavoriteChats = localStorage.getItem("shortcut-favoriteChats") || "Command+;";
if (checkShortcutMatch(e, shortcutFavoriteChats)) {
  e.preventDefault();
  e.stopPropagation();
  e.stopImmediatePropagation();
  
  // Ensure any existing blocks are cleaned up
  ensureInputUnblocked();
  
  // Проверяем, есть ли уже открытое окно
  const existingUI = document.getElementById('favorite-chats-notes-ui');
  if (existingUI) {
    // Если окно уже открыто, закрываем его
    if (document.body.contains(existingUI)) {
      existingUI.remove();
    }
    ensureInputUnblocked();
  } else {
    // Show the favorite chats UI directly without blocking global input
    showFavoriteChatsWithNotes();
  }
}
});

// Combined mousedown handler for both closing edit mode and handling UI
document.addEventListener('mousedown', function globalMousedownHandler(e) {
// First, check if there are any cards in edit mode
const editingCards = document.querySelectorAll('.favorite-chat-card.editing');
const uiContainer = document.getElementById('favorite-chats-notes-ui');

// If no UI container, just clean up and exit
if (!uiContainer) {
  if (editingCards.length === 0) {
    ensureInputUnblocked();
  }
  return;
}

// Grace period check
if (uiContainer.getAttribute('data-grace') === 'true') return;

// Timing check to avoid immediate closing
const creationTime = parseInt(uiContainer.getAttribute('data-creation-time')) || 0;
const currentTime = Date.now();
if (currentTime - creationTime < 500) return;

// If we have editing cards, clicking anywhere outside the editing card should just close 
// the edit mode but keep the UI visible, even if click is outside the entire UI
if (editingCards.length > 0) {
  // Check if the click is on an editing card or its children
  let clickedOnEditingCard = false;
  editingCards.forEach(card => {
    if (card.contains(e.target) || e.target === card) {
      clickedOnEditingCard = true;
    }
  });
  
  // If not clicking on an editing card and not on a button, close edit mode only
  if (!clickedOnEditingCard && !e.target.closest('button')) {
    e.preventDefault(); // Prevent default
    e.stopPropagation(); // Stop propagation
    
    // Close edit mode on all cards
    editingCards.forEach(card => {
      const closeBtn = card.querySelector('button[title="Закрыть редактирование"]');
      if (closeBtn) {
        closeBtn.click();
      } else {
        const saveBtn = Array.from(card.querySelectorAll('button')).find(btn => 
          btn.textContent === 'Закрыть');
        if (saveBtn) {
          saveBtn.click();
        }
      }
    });
    
    return false; // Stop handling
  }
  
  // If we're in edit mode, don't close the UI no matter where clicked
  return;
}

// Only if we're NOT in edit mode, handle normal UI closing when clicking outside
// Added check for .calendar-event-editor
if (!uiContainer.contains(e.target) && 
    !e.target.closest('.calendar-event-editor') && 
    !e.target.closest('[id^="shortcut"]') && 
    e.target.id !== 'favoriteChatsBtn' &&
    !e.target.matches('input, textarea')) {
  
  // Save the active tab before removing
  localStorage.setItem('telegram_last_active_tab', activeTab);
  if (document.body.contains(uiContainer)) {
    uiContainer.remove();
  }
  ensureInputUnblocked();
}
}, true); // Use capturing phase for reliable event handling

// Custom event handler specifically for Telegram input elements
document.addEventListener('click', function(e) {
// Only intercept clicks on the Telegram input if we're in edit mode
if (window.telegramInputBlocked && 
    (e.target.matches('.input-message-input, .input-message-container') || 
    e.target.closest('.input-message-input, .input-message-container'))) {
  
  // Check if we actually have any cards in edit mode
  const editingCards = document.querySelectorAll('.favorite-chat-card.editing');
  if (editingCards.length > 0) {
    e.stopPropagation();
    e.preventDefault();
    
    // Optional: you could flash the editing card to indicate that's where input should go
    editingCards.forEach(card => {
      const editField = card.querySelector('.notes-edit-field');
      if (editField) {
        // Flash the edit field to draw attention
        const originalBackground = editField.style.backgroundColor;
        editField.style.backgroundColor = '#3a539b';
        setTimeout(() => {
          editField.style.backgroundColor = originalBackground;
        }, 300);
        
        // Focus the edit field
        editField.focus();
      }
    });
    
    return false;
  } else {
    // If no cards are in edit mode, allow input
    ensureInputUnblocked();
  }
}
});

// Обработчик закрытия по Escape
document.addEventListener('keydown', function(e) {
if (e.key !== 'Escape') return;

const uiContainer = document.getElementById('favorite-chats-notes-ui');
if (!uiContainer) return;

if (uiContainer.getAttribute('data-grace') === 'true') return;

// Save the active tab before removing
localStorage.setItem('telegram_last_active_tab', activeTab);
if (document.body.contains(uiContainer)) {
  uiContainer.remove();
}
ensureInputUnblocked(); // Ensure input is unblocked
});

// Function to show favorite chat manager
function showFavoriteChatManager(targetRowId = null) {
console.log("showFavoriteChatManager called with targetRowId:", targetRowId);

// Store the target row for newly added cards
if (targetRowId) {
  localStorage.setItem('favorite_manager_target_row', targetRowId);
} else {
  localStorage.removeItem('favorite_manager_target_row');
}

// Check for existing UI
const existingManagerUI = document.getElementById('favorite-chat-manager-ui');
if (existingManagerUI) {
  existingManagerUI.remove();
}

// Create manager UI
const managerUI = document.createElement('div');
managerUI.id = 'favorite-chat-manager-ui';
managerUI.style.position = 'fixed';
managerUI.style.top = '50%';
managerUI.style.left = '50%';
managerUI.style.transform = 'translate(-50%, -50%)';
managerUI.style.width = '500px';
managerUI.style.maxWidth = '90vw';
managerUI.style.padding = '20px';
managerUI.style.backgroundColor = '#212452';
managerUI.style.borderRadius = '12px';
managerUI.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)';
managerUI.style.zIndex = '99999';
managerUI.style.color = 'white';
managerUI.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';

// Create header
const header = document.createElement('div');
header.style.display = 'flex';
header.style.justifyContent = 'space-between';
header.style.alignItems = 'center';
header.style.marginBottom = '20px';

const title = document.createElement('h2');
title.innerText = targetRowId ? `Добавить чат в "${targetRowId.replace(/-/g, ' ')}"` : 'Управление избранными чатами';
title.style.margin = '0';
title.style.fontSize = '18px';

const closeBtn = document.createElement('button');
closeBtn.innerHTML = '✕';
closeBtn.style.background = 'none';
closeBtn.style.border = 'none';
closeBtn.style.color = 'white';
closeBtn.style.fontSize = '18px';
closeBtn.style.cursor = 'pointer';
closeBtn.style.opacity = '0.7';
closeBtn.style.transition = 'opacity 0.2s';

closeBtn.addEventListener('mouseover', () => {
  closeBtn.style.opacity = '1';
});

closeBtn.addEventListener('mouseout', () => {
  closeBtn.style.opacity = '0.7';
});

closeBtn.addEventListener('click', () => {
  managerUI.remove();
  overlay.remove();
});

header.appendChild(title);
header.appendChild(closeBtn);
managerUI.appendChild(header);

// Create tabs
const tabs = document.createElement('div');
tabs.style.display = 'flex';
tabs.style.marginBottom = '20px';
tabs.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';

const addChatTab = document.createElement('div');
addChatTab.innerText = 'Добавить чат';
addChatTab.style.padding = '10px 15px';
addChatTab.style.cursor = 'pointer';
addChatTab.style.borderBottom = '2px solid #4b6cb7';
addChatTab.style.color = 'white';

const quickAddTab = document.createElement('div');
quickAddTab.innerText = 'Быстрое добавление';
quickAddTab.style.padding = '10px 15px';
quickAddTab.style.cursor = 'pointer';
quickAddTab.style.opacity = '0.7';
quickAddTab.style.borderBottom = '2px solid transparent';

const manageChatTab = document.createElement('div');
manageChatTab.innerText = 'Управление';
manageChatTab.style.padding = '10px 15px';
manageChatTab.style.cursor = 'pointer';
manageChatTab.style.opacity = '0.7';
manageChatTab.style.borderBottom = '2px solid transparent';

tabs.appendChild(addChatTab);
tabs.appendChild(quickAddTab);
tabs.appendChild(manageChatTab);
managerUI.appendChild(tabs);

// Content container for tab content
const contentContainer = document.createElement('div');
contentContainer.style.minHeight = '200px';
managerUI.appendChild(contentContainer);

// Function to switch tabs
function switchTab(activeTab) {
  console.log("Switching to tab:", activeTab.innerText);
  
  [addChatTab, quickAddTab, manageChatTab].forEach(tab => {
    tab.style.opacity = '0.7';
    tab.style.borderBottom = '2px solid transparent';
  });
  
  activeTab.style.opacity = '1';
  activeTab.style.borderBottom = '2px solid #4b6cb7';
  
  contentContainer.innerHTML = '';
  
  try {
    if (activeTab === addChatTab) {
      showAddChatForm();
    } else if (activeTab === quickAddTab) {
      showQuickAddForm();
    } else if (activeTab === manageChatTab) {
      showManageForm();
    }
  } catch (error) {
    console.error("Error showing tab content:", error);
    contentContainer.innerHTML = `<div style="color: #ff5555; padding: 20px; text-align: center;">Произошла ошибка при загрузке содержимого. Попробуйте обновить страницу.</div>`;
  }
}

addChatTab.addEventListener('click', () => switchTab(addChatTab));
quickAddTab.addEventListener('click', () => switchTab(quickAddTab));
manageChatTab.addEventListener('click', () => switchTab(manageChatTab));

// Standard Add Chat Form
function showAddChatForm() {
  const form = document.createElement('div');
  
  const nameLabel = document.createElement('div');
  nameLabel.innerText = 'Название чата:';
  nameLabel.style.marginBottom = '5px';
  
  const nameInput = document.createElement('input');
  nameInput.type = 'text';
  nameInput.placeholder = 'Введите название чата';
  nameInput.style.width = '100%';
  nameInput.style.padding = '10px';
  nameInput.style.marginBottom = '15px';
  nameInput.style.borderRadius = '5px';
  nameInput.style.border = '1px solid rgba(255, 255, 255, 0.2)';
  nameInput.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
  nameInput.style.color = 'white';
  nameInput.style.boxSizing = 'border-box';
  
  const idLabel = document.createElement('div');
  idLabel.innerText = 'ID чата:';
  idLabel.style.marginBottom = '5px';
  
  const idInput = document.createElement('input');
  idInput.type = 'text';
  idInput.placeholder = 'Введите ID чата (скопируйте из URL)';
  idInput.style.width = '100%';
  idInput.style.padding = '10px';
  idInput.style.marginBottom = '20px';
  idInput.style.borderRadius = '5px';
  idInput.style.border = '1px solid rgba(255, 255, 255, 0.2)';
  idInput.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
  idInput.style.color = 'white';
  idInput.style.boxSizing = 'border-box';
  
  const helpText = document.createElement('div');
  helpText.innerHTML = 'ID чата можно найти в адресной строке:<br>https://web.telegram.org/k/#-1234567890';
  helpText.style.fontSize = '12px';
  helpText.style.opacity = '0.7';
  helpText.style.marginBottom = '20px';
  
  const addButton = document.createElement('button');
  addButton.innerText = 'Добавить в избранное';
  addButton.style.padding = '10px 15px';
  addButton.style.backgroundColor = '#4b6cb7';
  addButton.style.color = 'white';
  addButton.style.border = 'none';
  addButton.style.borderRadius = '5px';
  addButton.style.cursor = 'pointer';
  addButton.style.width = '100%';
  
  addButton.addEventListener('click', () => {
    const name = nameInput.value.trim();
    const id = idInput.value.trim();
    
    if (!name) {
      showError('Введите название чата');
  return;
}

    if (!id) {
      showError('Введите ID чата');
      return;
    }
    
    addChatToFavorites(name, id);
  });
  
  form.appendChild(nameLabel);
  form.appendChild(nameInput);
  form.appendChild(idLabel);
  form.appendChild(idInput);
  form.appendChild(helpText);
  form.appendChild(addButton);
  
  contentContainer.appendChild(form);
  nameInput.focus();
}

// Quick Add Form (name only)
function showQuickAddForm() {
  const form = document.createElement('div');
  
  const nameLabel = document.createElement('div');
  nameLabel.innerText = 'Название чата:';
  nameLabel.style.marginBottom = '5px';
  
  const nameInput = document.createElement('input');
  nameInput.type = 'text';
  nameInput.placeholder = 'Введите название чата';
  nameInput.style.width = '100%';
  nameInput.style.padding = '10px';
  nameInput.style.marginBottom = '20px';
  nameInput.style.borderRadius = '5px';
  nameInput.style.border = '1px solid rgba(255, 255, 255, 0.2)';
  nameInput.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
  nameInput.style.color = 'white';
  nameInput.style.boxSizing = 'border-box';
  
  const helpText = document.createElement('div');
  helpText.innerHTML = 'Быстрое добавление позволяет создать карточку только с названием.<br>ID будет сгенерирован автоматически.';
  helpText.style.fontSize = '12px';
  helpText.style.opacity = '0.7';
  helpText.style.marginBottom = '20px';
  
  const addButton = document.createElement('button');
  addButton.innerText = 'Быстрое добавление';
  addButton.style.padding = '10px 15px';
  addButton.style.backgroundColor = '#4b6cb7';
  addButton.style.color = 'white';
  addButton.style.border = 'none';
  addButton.style.borderRadius = '5px';
  addButton.style.cursor = 'pointer';
  addButton.style.width = '100%';
  
  addButton.addEventListener('click', () => {
    const name = nameInput.value.trim();
    
    if (!name) {
      showError('Введите название чата');
      return;
    }
    
    // Generate a unique ID for this chat
    const uniqueId = 'quick_' + Date.now();
    addChatToFavorites(name, uniqueId);
  });
  
  form.appendChild(nameLabel);
  form.appendChild(nameInput);
  form.appendChild(helpText);
  form.appendChild(addButton);
  
  contentContainer.appendChild(form);
  nameInput.focus();
}

// Manage Chats Form
function showManageForm() {
  console.log("showManageForm called - initializing management interface");
  
  const form = document.createElement('div');
  
  // Initialize default rows to ensure we have proper row names
  initializeDefaultRows();
  
  // Get row configuration
  const rowConfiguration = JSON.parse(localStorage.getItem('row_configuration') || '{}');
  console.log("Row configuration:", rowConfiguration);
  
  // Get favorite chats and ensure each has an order property
  let favoriteChats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
  console.log("Loaded favorite chats:", favoriteChats);
  
  // Ensure each chat has an order property, adding if missing
  favoriteChats = favoriteChats.map((chat, index) => {
    if (chat.order === undefined) {
      chat.order = index * 10;
    }
    return chat;
  });
  
  // Save back with order properties
  localStorage.setItem('favorite_chats', JSON.stringify(favoriteChats));
  
  if (favoriteChats.length === 0) {
    const emptyMessage = document.createElement('div');
    emptyMessage.innerText = 'У вас нет избранных чатов';
    emptyMessage.style.textAlign = 'center';
    emptyMessage.style.padding = '20px';
    emptyMessage.style.opacity = '0.7';
    form.appendChild(emptyMessage);
    
    // Add help message
    const helpMessage = document.createElement('div');
    helpMessage.innerHTML = 'Чтобы добавить чат в избранное, перейдите на вкладку "Добавить чат"';
    helpMessage.style.textAlign = 'center';
    helpMessage.style.padding = '10px';
    helpMessage.style.marginTop = '10px';
    helpMessage.style.fontSize = '14px';
    helpMessage.style.opacity = '0.7';
    form.appendChild(helpMessage);
    
    // Add quick add button
    const quickAddButton = document.createElement('button');
    quickAddButton.innerText = 'Добавить чат';
    quickAddButton.style.marginTop = '20px';
    quickAddButton.style.padding = '10px 15px';
    quickAddButton.style.backgroundColor = '#4b6cb7';
    quickAddButton.style.color = 'white';
    quickAddButton.style.border = 'none';
    quickAddButton.style.borderRadius = '5px';
    quickAddButton.style.cursor = 'pointer';
    quickAddButton.style.width = '100%';
    
    quickAddButton.addEventListener('click', () => {
      switchTab(addChatTab);
    });
    
    form.appendChild(quickAddButton);
  } else {
    // Add instructions
    const instructions = document.createElement('div');
    instructions.innerHTML = 'Используйте кнопки ↑ и ↓ для изменения порядка чатов. Перетаскивайте чаты между группами в основном интерфейсе.';
    instructions.style.padding = '10px';
    instructions.style.marginBottom = '15px';
    instructions.style.backgroundColor = 'rgba(75, 108, 183, 0.1)';
    instructions.style.borderRadius = '5px';
    instructions.style.fontSize = '14px';
    form.appendChild(instructions);
    
    const chatsList = document.createElement('div');
    chatsList.id = 'favorite-chats-list';
    chatsList.style.maxHeight = '300px';
    chatsList.style.overflowY = 'auto';
    chatsList.style.border = '1px solid rgba(255, 255, 255, 0.1)';
    chatsList.style.borderRadius = '4px';
    
    // Function to render chats list
    function renderChatsList() {
      console.log("renderChatsList called - rendering chats list");
      
      // Clear existing list
      chatsList.innerHTML = '';
      
      // Get current row assignments
      const cardRowAssignments = JSON.parse(localStorage.getItem('card_row_assignments') || '{}');
      console.log("Card row assignments:", cardRowAssignments);
      
      // Sort chats by order
      const sortedChats = [...favoriteChats].sort((a, b) => a.order - b.order);
      console.log("Sorted chats:", sortedChats);
      
      if (sortedChats.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.innerText = 'Нет избранных чатов';
        emptyMessage.style.textAlign = 'center';
        emptyMessage.style.padding = '20px';
        emptyMessage.style.opacity = '0.7';
        chatsList.appendChild(emptyMessage);
        return;
      }
      
      // Render all chats in their sorted order
      sortedChats.forEach((chat, index) => {
        console.log(`Rendering chat ${index + 1}: ${chat.name} (${chat.id})`);
        
        const chatItem = document.createElement('div');
        chatItem.setAttribute('data-chat-id', chat.id);
        chatItem.setAttribute('data-index', index);
        chatItem.style.display = 'flex';
        chatItem.style.alignItems = 'center';
        chatItem.style.padding = '10px';
        chatItem.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
        
        // Order number
        const orderNum = document.createElement('div');
        orderNum.innerText = `${index + 1}.`;
        orderNum.style.marginRight = '8px';
        orderNum.style.opacity = '0.7';
        orderNum.style.fontSize = '12px';
        orderNum.style.width = '20px';
        
        // Chat info (name and ID)
        const chatInfo = document.createElement('div');
        chatInfo.style.flex = '1';
        
        const chatName = document.createElement('div');
        chatName.innerText = chat.name;
        chatName.style.fontWeight = 'bold';
        
        const chatId = document.createElement('div');
        chatId.innerText = chat.id;
        chatId.style.fontSize = '12px';
        chatId.style.opacity = '0.7';
        
        // Show which row this chat belongs to
        const rowInfo = document.createElement('div');
        const rowId = cardRowAssignments[chat.id] || 'избранные-чаты';
        const rowConfig = rowConfiguration[rowId] || {};
        rowInfo.innerText = rowConfig.name || rowId.replace(/-/g, ' ');
        rowInfo.style.fontSize = '11px';
        rowInfo.style.padding = '2px 6px';
        rowInfo.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        rowInfo.style.borderRadius = '10px';
        rowInfo.style.marginTop = '3px';
        
        chatInfo.appendChild(chatName);
        chatInfo.appendChild(chatId);
        chatInfo.appendChild(rowInfo);
        
        // Controls
        const controls = document.createElement('div');
        controls.style.display = 'flex';
        controls.style.gap = '8px';
        
        // Move up button
        const upBtn = document.createElement('button');
        upBtn.innerHTML = '↑';
        upBtn.title = 'Переместить выше';
        upBtn.style.background = 'none';
        upBtn.style.border = 'none';
        upBtn.style.color = 'white';
        upBtn.style.fontSize = '16px';
        upBtn.style.cursor = 'pointer';
        upBtn.style.opacity = '0.7';
        upBtn.disabled = index === 0;
        upBtn.style.opacity = index === 0 ? '0.3' : '0.7';
        
        upBtn.addEventListener('mouseover', () => {
          if (!upBtn.disabled) upBtn.style.opacity = '1';
        });
        
        upBtn.addEventListener('mouseout', () => {
          if (!upBtn.disabled) upBtn.style.opacity = '0.7';
        });
        
        upBtn.addEventListener('click', (e) => {
  e.stopPropagation();
          if (index === 0) return;
          
          console.log(`Moving chat "${chat.name}" up`);
          
          // Simple approach: Move this chat one position up in the array
          const tempChats = [...favoriteChats];
          tempChats.splice(index, 1); // Remove from current position
          tempChats.splice(index - 1, 0, chat); // Insert at previous position
          
          // Recalculate all order values based on array position
          tempChats.forEach((c, i) => {
            c.order = i * 10;
          });
          
          // Update the array
          favoriteChats = tempChats;
          
          // Save to localStorage
          localStorage.setItem('favorite_chats', JSON.stringify(favoriteChats));
          
          // Just rerender the list
          renderChatsList();
          
          // Show success feedback
          showPopup('Порядок чатов обновлен');
        });
        
        // Move down button
        const downBtn = document.createElement('button');
        downBtn.innerHTML = '↓';
        downBtn.title = 'Переместить ниже';
        downBtn.style.background = 'none';
        downBtn.style.border = 'none';
        downBtn.style.color = 'white';
        downBtn.style.fontSize = '16px';
        downBtn.style.cursor = 'pointer';
        downBtn.style.opacity = '0.7';
        downBtn.disabled = index === sortedChats.length - 1;
        downBtn.style.opacity = index === sortedChats.length - 1 ? '0.3' : '0.7';
        
        downBtn.addEventListener('mouseover', () => {
          if (!downBtn.disabled) downBtn.style.opacity = '1';
        });
        
        downBtn.addEventListener('mouseout', () => {
          if (!downBtn.disabled) downBtn.style.opacity = '0.7';
        });
        
        downBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          if (index === sortedChats.length - 1) return;
          
          console.log(`Moving chat "${chat.name}" down`);
          
          // Simple approach: Move this chat one position down in the array
          const tempChats = [...favoriteChats];
          tempChats.splice(index, 1); // Remove from current position
          tempChats.splice(index + 1, 0, chat); // Insert at next position
          
          // Recalculate all order values based on array position
          tempChats.forEach((c, i) => {
            c.order = i * 10;
          });
          
          // Update the array
          favoriteChats = tempChats;
          
          // Save to localStorage
          localStorage.setItem('favorite_chats', JSON.stringify(favoriteChats));
          
          // Just rerender the list
          renderChatsList();
          
          // Show success feedback
          showPopup('Порядок чатов обновлен');
        });
        
        // Delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.innerHTML = '🗑️';
        deleteBtn.title = 'Удалить чат из избранных';
        deleteBtn.style.background = 'none';
        deleteBtn.style.border = 'none';
        deleteBtn.style.color = 'white';
        deleteBtn.style.fontSize = '16px';
        deleteBtn.style.cursor = 'pointer';
        deleteBtn.style.opacity = '0.7';
        
        deleteBtn.addEventListener('mouseover', () => {
          deleteBtn.style.opacity = '1';
        });
        
        deleteBtn.addEventListener('mouseout', () => {
          deleteBtn.style.opacity = '0.7';
        });
        
        deleteBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          if (confirm(`Удалить чат "${chat.name}" из избранных?`)) {
            console.log(`Deleting chat "${chat.name}"`);
            
            // Remove from favorites array
            const chatIndex = favoriteChats.findIndex(c => c.id === chat.id);
            if (chatIndex !== -1) {
              favoriteChats.splice(chatIndex, 1);
              
              // Recalculate all order values based on array position
              favoriteChats.forEach((c, i) => {
                c.order = i * 10;
              });
              
              localStorage.setItem('favorite_chats', JSON.stringify(favoriteChats));
              
              // Remove row assignment
              const cardRowAssignments = JSON.parse(localStorage.getItem('card_row_assignments') || '{}');
              delete cardRowAssignments[chat.id];
              localStorage.setItem('card_row_assignments', JSON.stringify(cardRowAssignments));
              
              // If we're removing the last chat, rerender the whole UI
              if (favoriteChats.length === 0) {
                showManageForm();
                return;
              }
              
              // Remove this item only without redrawing the entire UI
              chatItem.style.height = chatItem.offsetHeight + 'px';
              chatItem.style.overflow = 'hidden';
              chatItem.style.transition = 'all 0.3s ease';
              
              setTimeout(() => {
                chatItem.style.height = '0';
                chatItem.style.padding = '0';
                
                setTimeout(() => {
                  chatItem.remove();
                  
                  // Just rerender the list
                  renderChatsList();
    }, 300);
              }, 0);
            }
          }
        });
        
        controls.appendChild(upBtn);
        controls.appendChild(downBtn);
        controls.appendChild(deleteBtn);
        
        chatItem.appendChild(orderNum);
        chatItem.appendChild(chatInfo);
        chatItem.appendChild(controls);
        chatsList.appendChild(chatItem);
      });
    }
    
    // Initial render
    renderChatsList();
    
    form.appendChild(chatsList);
    
    // Add explanation
    const helpText = document.createElement('div');
    helpText.innerHTML = 'Используйте кнопки ↑ и ↓ для изменения порядка чатов.<br>Перетаскивайте карточки между группами в основном интерфейсе.';
    helpText.style.fontSize = '12px';
    helpText.style.opacity = '0.7';
    helpText.style.marginTop = '10px';
    helpText.style.textAlign = 'center';
    form.appendChild(helpText);
  }
  
  contentContainer.appendChild(form);
}

// Helper function to show error
function showError(message) {
  const errorElement = contentContainer.querySelector('.error-message');
  if (errorElement) {
    errorElement.innerText = message;
    errorElement.style.display = 'block';
  } else {
    const error = document.createElement('div');
    error.className = 'error-message';
    error.innerText = message;
    error.style.color = '#ff5555';
    error.style.padding = '10px';
    error.style.marginBottom = '10px';
    error.style.backgroundColor = 'rgba(255, 85, 85, 0.1)';
    error.style.borderRadius = '5px';
    error.style.textAlign = 'center';
    contentContainer.insertBefore(error, contentContainer.firstChild);
  }
}

// Function to extract chat ID from URL
function extractChatIdFromUrl(url) {
  // Remove any whitespace
  url = url.trim();
  
  // Remove web.telegram.org/k/# prefix if present
  url = url.replace(/^https?:\/\/web\.telegram\.org\/k\/#/, '');
  
  // Remove any @ symbols
  url = url.replace(/@/g, '');
  
  // Extract the ID (can be numbers or letters with numbers)
  const match = url.match(/^([a-zA-Z0-9-]+)$/);
  
  if (match) {
    return match[1];
  }
  
  return null;
}

// Function to add chat to favorites
function addChatToFavorites(name, id) {
  try {
    // Extract chat ID from URL if it looks like a URL
    if (id.includes('telegram.org') || id.includes('/')) {
      const extractedId = extractChatIdFromUrl(id);
      if (extractedId) {
        id = extractedId;
      } else {
        showError('Неверный формат ID чата. Введите только ID или корректную ссылку на чат.');
        return;
      }
    }
    
    let favorites = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
    

    
    console.log(`Adding new chat: "${name}" (${id})`);
    
    // Get max order to add new chat at the end
    const maxOrder = favorites.reduce((max, chat) => Math.max(max, chat.order || 0), 0);
    
    // Create new chat
    const newChat = {
      id: id,
      name: name,
      order: maxOrder + 10
    };
    
    // Add to favorites
    favorites.push(newChat);
    
    // Save to localStorage
    localStorage.setItem('favorite_chats', JSON.stringify(favorites));
    
    // Check if there's a target row specified
    const targetRowId = localStorage.getItem('favorite_manager_target_row');
    console.log(`Adding card to row: ${targetRowId || 'default'}`);
    
    if (targetRowId) {
      // Add row assignment
      let cardRowAssignments = JSON.parse(localStorage.getItem('card_row_assignments') || '{}');
      cardRowAssignments[id] = targetRowId;
      localStorage.setItem('card_row_assignments', JSON.stringify(cardRowAssignments));
      
      // Log the assignment for debugging
      console.log(`Assigned card ${id} to row ${targetRowId}`);
    }
    
    // Show success message with row info
    const targetRowText = targetRowId ? ` в раздел "${targetRowId.replace(/-/g, ' ')}"` : '';
    managerUI.remove();
    overlay.remove();
    showPopup(`Чат "${name}" добавлен${targetRowText}`);
    
    // Refresh UI if it's open
    const uiContainer = document.getElementById('favorite-chats-notes-ui');
    if (uiContainer) {
      refreshCardRowAssignments();
    }
  } catch (error) {
    console.error("Error adding chat to favorites:", error);
    showError("Произошла ошибка при добавлении чата. Попробуйте перезагрузить страницу.");
  }
}

// Switch to the initial tab
if (targetRowId) {
  switchTab(quickAddTab);
} else {
  switchTab(addChatTab);
}

// Display the target row name clearly at the top
if (targetRowId) {
  const targetRowName = targetRowId.replace(/-/g, ' ');
  const targetRowIndicator = document.createElement('div');
  targetRowIndicator.style.cssText = `
    padding: 8px 12px;
    background-color: rgba(75, 108, 183, 0.2);
    border-radius: 5px;
    margin-bottom: 15px;
    font-size: 14px;
    text-align: center;
  `;
  targetRowIndicator.innerHTML = `<strong>Добавление в:</strong> ${targetRowName}`;
  
  contentContainer.insertBefore(targetRowIndicator, contentContainer.firstChild);
}

// Add to document
document.body.appendChild(managerUI);

// Add overlay to prevent clicking behind
const overlay = document.createElement('div');
overlay.style.position = 'fixed';
overlay.style.top = '0';
overlay.style.left = '0';
overlay.style.right = '0';
overlay.style.bottom = '0';
overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
overlay.style.zIndex = '99998';

overlay.addEventListener('click', () => {
  overlay.remove();
  managerUI.remove();
});

document.body.appendChild(overlay);
}

// Function to show favorite chats in the send interface
function modifyShareUI() {
// Wait for the UI to fully load
      setTimeout(() => {
  // Look for the share UI elements
  const shareContainer = document.querySelector('.tgme_widget_message_link_preview, .tgme_widget_message_forwarded');
  if (!shareContainer) return;
  
  // Check if we've already modified this UI
  if (shareContainer.querySelector('.favorite-chats-filter')) return;
  
  // Add a filter option at the top of the share UI
  const filterContainer = document.createElement('div');
  filterContainer.className = 'favorite-chats-filter';
  filterContainer.style.cssText = `
    padding: 10px 15px;
    background-color: #18222d;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  `;
  
  // Create toggle switch
  const toggleLabel = document.createElement('label');
  toggleLabel.style.cssText = `
    display: flex;
    align-items: center;
    cursor: pointer;
  `;
  
  const toggleInput = document.createElement('input');
  toggleInput.type = 'checkbox';
  toggleInput.style.cssText = `
    height: 0;
    width: 0;
    visibility: hidden;
    position: absolute;
  `;
  
  // Set initial state from localStorage
  toggleInput.checked = localStorage.getItem('show_only_favorites') === 'true';
  
  const toggleSpan = document.createElement('span');
  toggleSpan.style.cssText = `
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    background: ${toggleInput.checked ? '#4b6cb7' : '#333'};
    border-radius: 10px;
    transition: background 0.3s;
    margin-right: 10px;
  `;
  
  const toggleKnob = document.createElement('span');
  toggleKnob.style.cssText = `
    position: absolute;
    top: 2px;
    left: ${toggleInput.checked ? '22px' : '2px'};
    width: 16px;
    height: 16px;
    background: #fff;
    border-radius: 50%;
    transition: left 0.3s;
  `;
  
  const toggleText = document.createElement('span');
  toggleText.innerText = 'Только избранные чаты';
  toggleText.style.cssText = `
    font-size: 14px;
    color: #fff;
  `;
  
  toggleSpan.appendChild(toggleKnob);
  toggleLabel.appendChild(toggleInput);
  toggleLabel.appendChild(toggleSpan);
  toggleLabel.appendChild(toggleText);
  
  filterContainer.appendChild(toggleLabel);
  
  // Find the right place to insert our filter (before the list of chats)
  const chatList = shareContainer.querySelector('div[role="presentation"], .tgme_widget_message_forwarded_from');
  if (chatList && chatList.parentNode) {
    chatList.parentNode.insertBefore(filterContainer, chatList);
  } else {
    shareContainer.prepend(filterContainer);
  }
  
  // Function to filter chats
  function filterChats() {
    // Get all favorite chats
    const favoriteChats = JSON.parse(localStorage.getItem('favorite_chats') || '[]');
    const favoriteIds = favoriteChats.map(chat => chat.id);
    
    // Get all chat rows from the share UI
    const chatRows = document.querySelectorAll('.tgme_widget_message_forwarded_from_name, div[role="row"]');
    
    if (toggleInput.checked) {
      // Show only favorite chats
      chatRows.forEach(row => {
        // Get chat ID from the row (different for different UIs)
        let chatId = '';
        
        // For new UI
        const idCell = row.querySelector('[role="cell"]:nth-child(2)');
        if (idCell && idCell.textContent) {
          chatId = idCell.textContent.trim().replace('@', '');
        }
        
        // For old UI
        if (!chatId && row.dataset && row.dataset.username) {
          chatId = row.dataset.username;
        }
        
        // For old UI v2
        if (!chatId && row.href) {
          const match = row.href.match(/\/([^/]+)$/);
          if (match) chatId = match[1];
        }
        
        if (!chatId) return;
        
        const isFavorite = favoriteIds.includes(chatId) || 
                        favoriteIds.some(id => id === chatId.replace('@', ''));
        
        if (isFavorite) {
          row.style.display = '';
          // Highlight favorite chats
          row.style.backgroundColor = 'rgba(75, 108, 183, 0.1)';
        } else {
          row.style.display = 'none';
        }
      });
    } else {
      // Show all chats
      chatRows.forEach(row => {
        row.style.display = '';
        row.style.backgroundColor = '';
      });
    }
  }
  
  // Apply initial filtering
  filterChats();
  
  // Toggle filtering when the switch is clicked
  toggleInput.addEventListener('change', () => {
    toggleKnob.style.left = toggleInput.checked ? '22px' : '2px';
    toggleSpan.style.background = toggleInput.checked ? '#4b6cb7' : '#333';
    
    // Save to localStorage
    localStorage.setItem('show_only_favorites', toggleInput.checked);
    
    // Filter chats
    filterChats();
  });
  
  // Also listen for changes in the UI (like search results)
  const observer = new MutationObserver(() => {
    if (toggleInput.checked) {
      filterChats();
    }
  });
  
  if (chatList) {
    observer.observe(chatList, { childList: true, subtree: true });
  }
      }, 500);
}

// Watch for share UI to appear
function watchForShareUI() {
const observer = new MutationObserver((mutations) => {
  for (const mutation of mutations) {
    if (mutation.addedNodes.length) {
      if (document.querySelector('.tgme_widget_message_link_preview, .tgme_widget_message_forwarded')) {
        modifyShareUI();
        break;
      }
  }
}
});

observer.observe(document.body, { childList: true, subtree: true });

// Also check immediately
if (document.querySelector('.tgme_widget_message_link_preview, .tgme_widget_message_forwarded')) {
  modifyShareUI();
}
}

// Initialize share UI watcher
watchForShareUI();

// Check for specific Telegram Web UI when the page loads
document.addEventListener('DOMContentLoaded', () => {
// Initialize all the extension functionality

// Observer for watching when users click the share button in messages
watchForShareUI();
});

// Function to add a new row
function addNewRow(title) {
// Create ID from title
const rowId = title.toLowerCase().replace(/\s+/g, '-');

// Get existing rows configuration
let rowConfiguration = JSON.parse(localStorage.getItem('row_configuration') || '{}');

// Check if this row ID already exists
if (rowConfiguration[rowId]) {
  return false;
}

// Add new row
rowConfiguration[rowId] = {
  name: title,
  order: Object.keys(rowConfiguration).length + 1
};

// Save updated configuration
localStorage.setItem('row_configuration', JSON.stringify(rowConfiguration));

return rowId;
}

// Function to get all configured rows
function getConfiguredRows() {
// Ensure default rows exist
initializeDefaultRows();

// Get current configuration
const rowConfiguration = JSON.parse(localStorage.getItem('row_configuration') || '{}');

// Sort by order
return Object.entries(rowConfiguration)
  .sort((a, b) => a[1].order - b[1].order)
  .map(([id, config]) => ({
    id,
    name: config.name,
    order: config.order
  }));
}

// Initialize default rows if they don't exist yet
function initializeDefaultRows() {
let rowConfiguration = JSON.parse(localStorage.getItem('row_configuration') || '{}');
let needsUpdate = false;

// Default rows with their names
const defaultRows = {
  'избранные-чаты': { name: 'Избранные чаты', order: 1 },
  'рабочие-чаты': { name: 'Рабочие чаты', order: 2 },
  'личные-чаты': { name: 'Личные чаты', order: 3 }
};

// Add any missing default rows
for (const [id, config] of Object.entries(defaultRows)) {
  if (!rowConfiguration[id]) {
    rowConfiguration[id] = config;
    needsUpdate = true;
  }
}

// Save if changes were made
if (needsUpdate) {
  localStorage.setItem('row_configuration', JSON.stringify(rowConfiguration));
  console.log('Initialized default row configuration');
}
}

// Call this function early to ensure row configuration exists
initializeDefaultRows();

// Функция для сохранения связанных чатов
function saveRelatedChats(parentChatId, relatedChats) {
localStorage.setItem(`telegram_related_chats_${parentChatId}`, JSON.stringify(relatedChats));
}

// Функция для загрузки связанных чатов
function loadRelatedChats(parentChatId) {
const saved = localStorage.getItem(`telegram_related_chats_${parentChatId}`);
return saved ? JSON.parse(saved) : [];
}

// Function to show a modal for picking a related chat
function showRelatedChatPicker(parentChatId, containerToUpdate) {
// Create modal overlay
const overlay = document.createElement('div');
overlay.style.cssText = `
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999;
`;

// Create modal
const modal = document.createElement('div');
modal.style.cssText = `
  background-color: #212452;
  border-radius: 8px;
  padding: 20px;
  width: 300px;
  max-width: 90%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 100000;
`;

// Modal title
const title = document.createElement('h3');
title.textContent = 'Добавить связанный чат';
title.style.cssText = `
  color: white;
  margin-top: 0;
  font-size: 16px;
  margin-bottom: 15px;
`;

// Create search input
const searchInput = document.createElement('input');
searchInput.type = 'text';
searchInput.placeholder = 'Введите URL или ID чата';
searchInput.style.cssText = `
  width: 100%;
  padding: 8px 10px;
  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(0, 0, 0, 0.2);
  color: white;
  border-radius: 4px;
  margin-bottom: 10px;
`;

// Create name input
const nameInput = document.createElement('input');
nameInput.type = 'text';
nameInput.placeholder = 'Название чата (опционально)';
nameInput.style.cssText = `
  width: 100%;
  padding: 8px 10px;
  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(0, 0, 0, 0.2);
  color: white;
  border-radius: 4px;
  margin-bottom: 15px;
`;

// Buttons container
const buttonsContainer = document.createElement('div');
buttonsContainer.style.cssText = `
  display: flex;
  justify-content: space-between;
`;

// Cancel button
const cancelButton = document.createElement('button');
cancelButton.textContent = 'Отмена';
cancelButton.style.cssText = `
  padding: 8px 15px;
  border: none;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 4px;
  cursor: pointer;
`;

// Add button
const addButton = document.createElement('button');
addButton.textContent = 'Добавить';
addButton.style.cssText = `
  padding: 8px 15px;
  border: none;
  background-color: rgba(75, 108, 183, 0.7);
  color: white;
  border-radius: 4px;
  cursor: pointer;
`;

// Close modal
function closeModal() {
  document.body.removeChild(overlay);
}

// Handle cancel button click
cancelButton.addEventListener('click', closeModal);

// Handle add button click
addButton.addEventListener('click', () => {
  const chatUrl = searchInput.value.trim();
  if (!chatUrl) {
    showPopup('Введите URL или ID чата');
    return;
  }
  
  // Extract chat ID from URL
  const chatId = extractChatIdFromUrl(chatUrl);
  
  if (!chatId) {
    showPopup('Неверный формат URL или ID чата');
    return;
  }
  
  // Get related chats
  const relatedChats = loadRelatedChats(parentChatId);
  
  // --- ПРОВЕРКА НА ДУБЛИКАТЫ УДАЛЕНА ---
  
  // Get name
  let chatName = nameInput.value.trim();
  if (!chatName) {
    // Extract name from URL if not provided
    const urlParts = chatUrl.split('/');
    chatName = urlParts[urlParts.length - 1] || 'Чат';
  }
  
  // Add to related chats
  relatedChats.push({
    id: chatId,
    name: chatName
  });
  
  // Save related chats
  saveRelatedChats(parentChatId, relatedChats);
  
  // Update UI
  const miniButton = document.createElement('div');
  miniButton.className = 'related-chat-button';
  miniButton.title = chatName;
  miniButton.style.cssText = `
    padding: 2px 8px;
    background-color: rgba(75, 108, 183, 0.3);
    border-radius: 4px;
    color: white;
    font-size: 11px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
    display: flex;
    align-items: center;
  `;
  miniButton.innerHTML = `
    <span style="overflow: hidden; text-overflow: ellipsis;">${chatName}</span>
  `;
  
  // Add click handler to open the related chat
  miniButton.addEventListener('click', (e) => {
    e.stopPropagation(); // Prevent triggering the parent card's click
    window.location.hash = chatId;
  });
  
  // Add delete button when hovering
  miniButton.addEventListener('mouseenter', () => {
    const deleteBtn = document.createElement('span');
    deleteBtn.className = 'delete-related-chat';
    deleteBtn.innerHTML = '×';
    deleteBtn.style.cssText = `
      margin-left: 4px;
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;
      font-weight: bold;
    `;
    
    deleteBtn.addEventListener('click', (e) => {
      e.stopPropagation(); // Prevent navigation
      
      // Запрашиваем подтверждение на удаление
      if (confirm(`Вы уверены, что хотите удалить связанный чат "${chatName}"?`)) {
        // Remove the related chat from the list
        const updatedRelatedChats = loadRelatedChats(parentChatId).filter(rc => rc.id !== chatId);
        saveRelatedChats(parentChatId, updatedRelatedChats);
        
        // Remove the button from UI
        miniButton.remove();
        
        // Показываем два уведомления: сначала алерт, затем всплывающее уведомление
        alert(`Связанный чат "${chatName}" удален`);
        showPopup('Связанный чат удален');
      }
    });
    
    miniButton.appendChild(deleteBtn);
  });
  
  // Remove delete button when not hovering
  miniButton.addEventListener('mouseleave', () => {
    const deleteBtn = miniButton.querySelector('.delete-related-chat');
    if (deleteBtn) deleteBtn.remove();
  });
  
  // Insert before the add button
  containerToUpdate.insertBefore(miniButton, containerToUpdate.lastChild);
  
  // Close modal
  closeModal();
  
  showPopup('Связанный чат добавлен');
});

// Add buttons to container
buttonsContainer.appendChild(cancelButton);
buttonsContainer.appendChild(addButton);

// Add elements to modal
modal.appendChild(title);
modal.appendChild(searchInput);
modal.appendChild(nameInput);
modal.appendChild(buttonsContainer);

// Add modal to overlay
overlay.appendChild(modal);

// Add overlay to body
document.body.appendChild(overlay);

// Focus search input
setTimeout(() => searchInput.focus(), 100);

// Close on overlay click
overlay.addEventListener('click', (e) => {
  if (e.target === overlay) {
    closeModal();
  }
});
}

// Function to extract chat ID from URL or text
function extractChatIdFromUrl(url) {
if (!url) return null;

// Try to extract as direct ID
if (url.match(/^-?\d+$/)) {
  return url;
}

// Try to extract from telegram URL formats
// https://t.me/c/123456789
const channelMatch = url.match(/t\.me\/c\/(\d+)/);
if (channelMatch) {
  return `-${channelMatch[1]}`;
}

// https://t.me/username
const usernameMatch = url.match(/t\.me\/([a-zA-Z0-9_]+)/);
if (usernameMatch) {
  return usernameMatch[1];
}

// Extract from URL hash
const hashMatch = url.match(/#-?(\d+)$/);
if (hashMatch) {
  return hashMatch[0].substring(1); // Remove # sign
}

// If no matches found, just return the original string as last resort
return url;
}

// Function to update the height of general notes based on collapsed rows
function updateGeneralNotesHeight() {
const generalNotesContainer = document.getElementById('general-notes-container');
if (!generalNotesContainer) return;

// Calculate the new height
const newHeight = calculateContentHeight();

// Find the currently active pane (either notes field or calendar)
const activePane = generalNotesContainer.querySelector('.general-notes-edit-field[style*="display: block"], .calendar-widget-container[style*="display: block"]');

// Apply the new height to the active pane
if (activePane) {
  activePane.style.height = newHeight;
}

console.log(`Обновлена высота активной панели: ${newHeight}`);
}

// Initialize the height of the initially active pane
setTimeout(() => {
const initialActivePane = document.querySelector('.general-notes-edit-field[data-tab="main"], .calendar-widget-container[data-tab="main"]'); // Check for main tab initially
if (initialActivePane) {
  initialActivePane.style.height = calculateContentHeight();
  if (initialActivePane.classList.contains('calendar-widget-container')) {
    displayCalendarWidget(); // Display calendar if it's the initial tab
  }
}
}, 100); // Delay slightly to ensure UI elements are ready

// --- Calendar Widget Functions START ---

function generateMonthCalendar(year, month, today) {
const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
// Russian month names
const monthNamesRu = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
// Emojis for months
const monthEmojis = ["❄️", "🌱", "🌸", "☀️", "⛱️", "🍉", "🍦", "🍂", "🎃", "🍁", "🦃", "🎄"]; // Feel free to adjust these emojis!
const dayNames = ["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"];
const dayNamesRu = ["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"]; // Russian day names

const firstDay = new Date(year, month, 1);
const lastDay = new Date(year, month + 1, 0);
const daysInMonth = lastDay.getDate();
// Adjust start day: 0 (Sun) -> 6, 1 (Mon) -> 0, etc.
const startDayOfWeek = (firstDay.getDay() + 6) % 7;

let html = `<div class="calendar-month">`;
// Use Russian names and add emoji
html += `<h4 class="calendar-month-header">${monthEmojis[month]} ${monthNamesRu[month]} ${year}</h4>`;
html += `<div class="calendar-grid">`;

// Day names header (Russian)
dayNamesRu.forEach(day => {
  html += `<div class="calendar-day-header">${day}</div>`;
});

// Empty cells before the first day
for (let i = 0; i < startDayOfWeek; i++) {
  html += `<div class="calendar-day empty"></div>`;
}

// Calendar days
for (let day = 1; day <= daysInMonth; day++) {
  let className = "calendar-day";
  const currentDate = new Date(year, month, day);
  const dateString = getCalendarEventKey(currentDate).replace('calendar_event_', ''); // Get YYYY-MM-DD
  const eventText = loadCalendarEvent(currentDate);

  if (year === today.getFullYear() && month === today.getMonth() && day === today.getDate()) {
    className += " today";
  }
  if (eventText) {
    className += " has-event";
  }
  // Remove inline handlers, just add data-date
  html += `<div class="${className}" data-date="${dateString}">${day}</div>`;
}

// Empty cells after the last day to fill the grid (optional, for alignment)
const totalCells = startDayOfWeek + daysInMonth;
const remainingCells = (7 - (totalCells % 7)) % 7;
for (let i = 0; i < remainingCells; i++) {
  html += `<div class="calendar-day empty"></div>`;
}


html += `</div></div>`; // Close calendar-grid and calendar-month
return html;
}

function displayCalendarWidget() {
const container = document.querySelector('.calendar-widget-container');
if (!container) return;

const today = new Date();
const currentYear = today.getFullYear();

let calendarHtml = `
  <style>
    .calendar-widget-content {
      display: flex;
      flex-direction: column;
      gap: 15px;
      padding: 10px;
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      height: 100%;
      overflow-y: auto;
      overflow-x: hidden;
      box-sizing: border-box;
      background-color: rgba(13, 32, 74, 0.86);
      scrollbar-width: thin;
      scrollbar-color: rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0.2);
    }
    /* For Webkit browsers like Chrome/Safari */
    .calendar-widget-content::-webkit-scrollbar {
      width: 8px;
    }
    .calendar-widget-content::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }
    .calendar-widget-content::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 4px;
    }
    .calendar-month {
      background-color: rgba(12, 27, 60, 0.86); /* Made darker */
      border-radius: 8px;
      padding: 10px;
      margin-bottom: 5px;
    }
    .calendar-month-header {
      text-align: center;
      margin: 0 0 10px 0;
      font-size: 16px;
      color: #ff9a7b; /* Coral color for month header */
    }
    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 4px; /* Smaller gap */
      font-size: 12px; /* Smaller font size */
      text-align: center;
    }
    .calendar-day-header {
      font-weight: bold;
      opacity: 0.7;
      padding-bottom: 4px;
    }
    .calendar-day {
      padding: 4px 0; /* Reduced padding */
      border-radius: 4px;
      position: relative; /* Needed for positioning the event indicator */
      cursor: pointer; /* Indicate days are clickable */
      transition: background-color 0.2s ease;
    }
    .calendar-day:not(.empty):hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
    .calendar-day.empty {
      background-color: transparent;
      cursor: default;
    }
    .calendar-day.today {
      background-color: #ff6b6b; /* Highlight color for today */
      color: black;
      font-weight: bold;
      border-radius: 6px; /* Make today a rounded rectangle */
    }
    /* Style for days with events */
    .calendar-day.has-event::after {
      content: '';
      position: absolute;
      bottom: 2px;
      left: 50%;
      transform: translateX(-50%);
      width: 4px;
      height: 4px;
      background-color: #ff9a7b; /* Use header color for indicator */
      border-radius: 50%;
    }
    /* Style for the event editor popup */
    .calendar-event-editor {
      position: absolute;
      background-color: #2d3165; /* Darker background */
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 6px;
      padding: 10px;
      z-index: 100001; /* Above calendar */
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;
      gap: 8px;
      width: 250px; /* Fixed width */
    }
    .calendar-event-editor textarea {
      width: 100%;
      height: 80px;
      background-color: rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 4px;
      color: white;
      font-size: 13px;
      padding: 5px;
      resize: vertical;
      box-sizing: border-box;
      font-family: inherit;
    }
    .calendar-event-editor .editor-buttons {
      display: flex;
      justify-content: space-between;
    }
    .calendar-event-editor button {
      padding: 5px 10px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: background-color 0.2s ease;
    }
    .calendar-event-editor .save-event-btn {
      background-color: #4b6cb7;
      color: white;
    }
    .calendar-event-editor .save-event-btn:hover {
      background-color: #3a539b;
    }
    .calendar-event-editor .delete-event-btn {
      background-color: #ff6b6b; /* Red color */
      color: black;
    }
    .calendar-event-editor .delete-event-btn:hover {
      background-color: #e05252;
    }
    .calendar-event-editor .cancel-event-btn {
      background-color: rgba(255, 255, 255, 0.2);
      color: white;
    }
    .calendar-event-editor .cancel-event-btn:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }
    /* Simple Tooltip Style */
    #calendar-tooltip {
      position: fixed;
      background-color: rgba(0, 0, 0, 0.85);
      color: white;
      padding: 5px 10px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 100002; /* Above editor */
      pointer-events: none; /* Don't interfere with mouse */
      white-space: pre-wrap; /* Respect newlines in event text */
      max-width: 250px;
      display: none; /* Hidden by default */
    }
    .year-header {
      text-align: center;
      color: white;
      font-size: 20px;
      margin: 0 0 15px 0;
      font-weight: bold;
      padding: 5px;
      background-color: rgba(32, 46, 84, 0.9);
      border-radius: 6px;
    }
    .months-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      padding-bottom: 20px; /* Add padding at bottom for better scrolling */
    }
    @media (max-width: 600px) {
      .months-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
  <div class="calendar-widget-content">
    <div class="year-header">${currentYear}</div>
    <div class="months-grid">
`;

// Generate all 12 months of the current year
for (let month = 0; month < 12; month++) {
  calendarHtml += generateMonthCalendar(currentYear, month, today);
}

calendarHtml += `</div></div>`; // Close months-grid and calendar-widget-content

container.innerHTML = calendarHtml;

// --- Add Event Listeners using Delegation --- 
const calendarContent = container.querySelector('.calendar-widget-content');
if (calendarContent) {
  calendarContent.addEventListener('click', (event) => {
    const dayElement = event.target.closest('.calendar-day:not(.empty)');
    if (dayElement) {
      const dateString = dayElement.getAttribute('data-date');
      handleCalendarDayClick(dayElement, dateString);
    }
  });

  calendarContent.addEventListener('mouseover', (event) => {
    const dayElement = event.target.closest('.calendar-day.has-event');
    if (dayElement) {
      const dateString = dayElement.getAttribute('data-date');
      // Pass the original mouse event for positioning
      handleCalendarDayMouseover(dayElement, dateString, event); 
    }
  });

  calendarContent.addEventListener('mouseout', (event) => {
    const dayElement = event.target.closest('.calendar-day.has-event');
    if (dayElement) {
      // Pass the element itself
      handleCalendarDayMouseout(dayElement); 
    }
  });
}
// --- End Event Listeners ---

// Scroll to current month (with a small offset to see it better)
setTimeout(() => {
  const currentMonthEl = container.querySelector(`.calendar-month:nth-child(${today.getMonth() + 1})`);
  if (currentMonthEl) {
    const calendarContent = container.querySelector('.calendar-widget-content');
    if (calendarContent) {
      // Calculate position to scroll to (position of current month minus some offset)
      const scrollToPosition = currentMonthEl.offsetTop - 50;
      calendarContent.scrollTop = Math.max(0, scrollToPosition);
    }
  }
}, 50);
}

// --- Calendar Widget Functions END ---

// --- Calendar Event Storage START ---
function getCalendarEventKey(date) {
// Format date as YYYY-MM-DD for a consistent key
const year = date.getFullYear();
const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Add 1 because months are 0-indexed
const day = date.getDate().toString().padStart(2, '0');
return `calendar_event_${year}-${month}-${day}`;
}

function saveCalendarEvent(date, eventText) {
const key = getCalendarEventKey(date);
if (eventText && eventText.trim() !== '') {
  localStorage.setItem(key, eventText.trim());
} else {
  // Remove the item if the text is empty to signify no event
  localStorage.removeItem(key);
}
}

function loadCalendarEvent(date) {
const key = getCalendarEventKey(date);
return localStorage.getItem(key) || ''; // Return empty string if no event
}
// --- Calendar Event Storage END ---

// --- Calendar Event Editor & Tooltip Logic START ---
let currentEventEditor = null;
let calendarTooltip = null;

function showCalendarTooltip(text, event) {
if (!calendarTooltip) {
  calendarTooltip = document.createElement('div');
  calendarTooltip.id = 'calendar-tooltip';
  document.body.appendChild(calendarTooltip);
}
calendarTooltip.innerHTML = text; // Use innerHTML to render potential <br> tags from newlines
calendarTooltip.style.display = 'block';
// Position tooltip near the mouse cursor
const xOffset = 10;
const yOffset = 10;
let left = event.clientX + xOffset;
let top = event.clientY + yOffset;

// Adjust if tooltip goes off-screen
const tooltipRect = calendarTooltip.getBoundingClientRect();
if (left + tooltipRect.width > window.innerWidth) {
  left = event.clientX - tooltipRect.width - xOffset;
}
if (top + tooltipRect.height > window.innerHeight) {
  top = event.clientY - tooltipRect.height - yOffset;
}

calendarTooltip.style.left = `${left}px`;
calendarTooltip.style.top = `${top}px`;
}

function hideCalendarTooltip() {
if (calendarTooltip) {
  calendarTooltip.style.display = 'none';
}
}

function closeEventEditor() {
if (currentEventEditor) {
  currentEventEditor.remove();
  currentEventEditor = null;
}
}

// Close editor if clicking outside
document.addEventListener('click', (event) => {
if (currentEventEditor && !currentEventEditor.contains(event.target) && !event.target.closest('.calendar-day')) {
  closeEventEditor();
}
}, true); // Use capture phase

function showEventEditor(dayElement, dateString) {
console.log('showEventEditor called for date:', dateString);
closeEventEditor(); // Close any existing editor

const date = new Date(dateString + 'T00:00:00'); // Ensure correct date parsing
const eventText = loadCalendarEvent(date);

currentEventEditor = document.createElement('div');
currentEventEditor.className = 'calendar-event-editor';

const textArea = document.createElement('textarea');
textArea.placeholder = 'Enter event...';
textArea.value = eventText;
// Stop keydown events from bubbling up from the textarea
textArea.addEventListener('keydown', (e) => {
  e.stopPropagation();
});

const buttonsDiv = document.createElement('div');
buttonsDiv.className = 'editor-buttons';

const saveBtn = document.createElement('button');
saveBtn.textContent = 'Save';
saveBtn.className = 'save-event-btn';
saveBtn.onclick = () => {
  saveCalendarEvent(date, textArea.value);
  // Update day element class
  if (textArea.value.trim()) {
    dayElement.classList.add('has-event');
  } else {
    dayElement.classList.remove('has-event');
  }
  closeEventEditor();
};

const deleteBtn = document.createElement('button');
deleteBtn.textContent = 'Delete';
deleteBtn.className = 'delete-event-btn';
deleteBtn.style.display = eventText ? 'inline-block' : 'none'; // Only show if event exists
deleteBtn.onclick = () => {
  saveCalendarEvent(date, ''); // Save empty string to delete
  dayElement.classList.remove('has-event');
  closeEventEditor();
};

const cancelBtn = document.createElement('button');
cancelBtn.textContent = 'Cancel';
cancelBtn.className = 'cancel-event-btn';
cancelBtn.onclick = closeEventEditor;

buttonsDiv.appendChild(saveBtn);
buttonsDiv.appendChild(deleteBtn);
buttonsDiv.appendChild(cancelBtn);

currentEventEditor.appendChild(textArea);
currentEventEditor.appendChild(buttonsDiv);

document.body.appendChild(currentEventEditor);

// Position the editor near the clicked day
const dayRect = dayElement.getBoundingClientRect();
let top = dayRect.bottom + window.scrollY + 5; // Position below the day
let left = dayRect.left + window.scrollX;

// Adjust if it goes off screen
const editorRect = currentEventEditor.getBoundingClientRect();
if (left + editorRect.width > window.innerWidth) {
  left = window.innerWidth - editorRect.width - 10; // Add some margin
}
if (top + editorRect.height > window.innerHeight) {
  top = dayRect.top + window.scrollY - editorRect.height - 5; // Position above the day
}

currentEventEditor.style.top = `${top}px`;
currentEventEditor.style.left = `${left}px`;

textArea.focus();
}

// Functions called by inline event handlers
function handleCalendarDayClick(element, dateString) {
console.log('handleCalendarDayClick called with:', element, dateString);
if (element.classList.contains('empty')) {
  console.log('Clicked on empty day, returning.');
  return; // Ignore empty days
}
showEventEditor(element, dateString);
}

function handleCalendarDayMouseover(element, dateString, event) {
if (element.classList.contains('has-event')) {
  const date = new Date(dateString + 'T00:00:00');
  const eventText = loadCalendarEvent(date);
  if (eventText) {
    // Pass the mouse event to position the tooltip correctly
    showCalendarTooltip(eventText, event);
  }
}
}

function handleCalendarDayMouseout(element) {
hideCalendarTooltip();
}
// --- Calendar Event Editor & Tooltip Logic END ---

// --- Assign handlers to global scope for inline calls --- 
window.handleCalendarDayClick = handleCalendarDayClick;
window.handleCalendarDayMouseover = handleCalendarDayMouseover;
window.handleCalendarDayMouseout = handleCalendarDayMouseout;




  })();
