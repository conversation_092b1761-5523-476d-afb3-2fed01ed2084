# Changelog - Telegram Log Extension

## Новые функции (2025-06-20)

### 🆕 Подзаметки (Под-табы)
- **Боковая панель подзаметок**: Добавлена панель справа от основной заметки
- **Управление подзаметками**: 
  - Кнопка "+" для создания новых подзаметок
  - Клик по подзаметке для переключения
  - Кнопка "×" для удаления подзаметок
- **Автосохранение**: Подзаметки автоматически сохраняются при редактировании
- **Превью**: Показывается название и краткий превью содержимого
- **Индивидуальное хранение**: Каждый чат имеет свои подзаметки

### 📤 Экспорт заметки с подзаметками
- **Новая кнопка экспорта**: Кнопка 📤 в панели инструментов заметок
- **Полный экспорт**: Включает основную заметку + все подзаметки
- **Структурированный формат**: 
  - Заголовок с информацией о чате и дате
  - Раздел основной заметки
  - Разделы для каждой подзаметки с нумерацией
- **Текстовый файл**: Сохранение в .txt файл с уникальным именем

### 🔗 Улучшенная обработка ссылок в экспорте
- **Формат "название (URL)"**: Ссылки теперь экспортируются с названием и полным URL
- **Применяется везде**: 
  - Экспорт заметок
  - Копирование/сохранение чатов
  - AI анализ сообщений
- **Умная обработка**: Различает текст ссылки и URL для правильного отображения

## Технические изменения

### Структура HTML
- Новый layout с `.notes-container`, `.main-note-section`, `.sub-notes-section`
- Добавлены элементы управления подзаметками
- Кнопка экспорта в toolbar

### CSS стили
- Стили для боковой панели подзаметок
- Активные состояния для выбранных подзаметок
- Кнопки управления с hover эффектами
- Адаптивный дизайн для разных размеров

### JavaScript функции
- `loadSubNotes()`, `saveSubNotes()` - управление данными
- `renderSubNotes()`, `createSubNoteElement()` - отрисовка UI
- `selectSubNote()`, `addSubNote()`, `deleteSubNote()` - взаимодействие
- `exportNoteWithSubNotes()` - экспорт функциональность
- `processMessageText()` - улучшенная обработка ссылок
- `convertHtmlToText()` - конвертация HTML в текст для экспорта

### Хранение данных
- Подзаметки: `telegram_sub_notes_{chatId}`
- JSON формат с полями: title, content, created
- Автоматическое сохранение при изменениях

## Использование

### Создание подзаметки
1. Откройте вкладку "Заметки" (✍)
2. Нажмите кнопку "+" в панели "Подзаметки"
3. Введите название подзаметки
4. Начните редактирование в основной области

### Переключение между заметками
- Клик по подзаметке в боковой панели
- Клик в пустой области панели для возврата к основной заметке

### Экспорт заметки
1. Нажмите кнопку 📤 в панели инструментов
2. Файл автоматически сохранится в папку загрузок
3. Имя файла: `note-export-{chatId}-{timestamp}.txt`

### Удаление подзаметки
1. Наведите курсор на подзаметку
2. Нажмите кнопку "×"
3. Подтвердите удаление

## Совместимость
- Работает со всеми существующими функциями
- Сохраняет обратную совместимость с основными заметками
- Поддерживает все форматы ссылок и HTML контент
- Интегрируется с системой фолдаутов и URL привязки

## Файлы изменений
- `content.js` - основные изменения функциональности
- `test.html` - тестовая страница для проверки функций
- `CHANGELOG.md` - этот файл с описанием изменений
