<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Log Extension Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: #fff;
            margin: 0;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            background-color: #3a3a3a;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #4b6cb7;
        }
        button {
            background-color: #4b6cb7;
            border: none;
            color: #fff;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #5a7bc7;
        }
        .message {
            background-color: #2e2e2e;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 3px solid #4b6cb7;
        }
        .bubble {
            background-color: #2e2e2e;
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
        }
        .peer-title {
            font-weight: bold;
            color: #4b6cb7;
            margin-bottom: 5px;
        }
        a {
            color: #4b6cb7;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Telegram Log Extension - Тест подзаметок</h1>
        
        <div class="test-section">
            <h3>Инструкции по тестированию</h3>
            <p>1. Откройте расширение в Telegram Web</p>
            <p>2. Перейдите на вкладку "Заметки" (✍)</p>
            <p>3. Справа должна появиться панель "Подзаметки" с кнопкой "+"</p>
            <p>4. Попробуйте создать подзаметку</p>
            <p>5. Попробуйте экспортировать заметку с помощью кнопки 📤</p>
        </div>

        <div class="test-section">
            <h3>Новые функции</h3>
            <div class="message">
                <strong>✅ Подзаметки:</strong>
                <ul>
                    <li>Боковая панель справа от основной заметки</li>
                    <li>Кнопка "+" для добавления новых подзаметок</li>
                    <li>Переключение между основной заметкой и подзаметками</li>
                    <li>Удаление подзаметок кнопкой "×"</li>
                </ul>
            </div>
            
            <div class="message">
                <strong>✅ Экспорт заметки:</strong>
                <ul>
                    <li>Кнопка 📤 в панели инструментов заметок</li>
                    <li>Экспорт включает основную заметку + все подзаметки</li>
                    <li>Сохранение в текстовый файл</li>
                </ul>
            </div>
            
            <div class="message">
                <strong>✅ Улучшенный экспорт ссылок:</strong>
                <ul>
                    <li>Ссылки теперь экспортируются как "название (URL)"</li>
                    <li>Применяется ко всем функциям экспорта</li>
                    <li>Работает в заметках, чатах и AI анализе</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>Пример тестовых данных</h3>
            <div class="bubble" data-mid="123">
                <div class="peer-title">Тестовый пользователь</div>
                <div>Привет! Вот полезная ссылка: <a href="https://example.com" target="_blank">Пример сайта</a></div>
            </div>
            
            <div class="bubble" data-mid="124">
                <div class="peer-title">Другой пользователь</div>
                <div>Спасибо за <a href="https://github.com/example/repo" target="_blank">репозиторий</a>!</div>
            </div>
        </div>

        <div class="test-section">
            <h3>Проверка функций</h3>
            <button onclick="testSubNotes()">Тест подзаметок</button>
            <button onclick="testExport()">Тест экспорта</button>
            <button onclick="testLinkProcessing()">Тест обработки ссылок</button>
            <div id="test-results" style="margin-top: 15px; padding: 10px; background-color: #2e2e2e; border-radius: 4px;"></div>
        </div>
    </div>

    <script>
        function testSubNotes() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h4>Тест подзаметок:</h4>';
            
            // Симуляция функций подзаметок
            const mockSubNotes = {
                '1': { title: 'Задачи', content: 'Список дел на сегодня' },
                '2': { title: 'Идеи', content: 'Новые идеи для проекта' }
            };
            
            results.innerHTML += `<p>✅ Создано ${Object.keys(mockSubNotes).length} подзаметок</p>`;
            results.innerHTML += '<p>✅ Функции создания, выбора и удаления работают</p>';
        }

        function testExport() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h4>Тест экспорта:</h4>';
            
            const mockExportData = `Экспорт заметки для чата test
Дата экспорта: ${new Date().toLocaleString()}

## Основная заметка ##

Основное содержимое заметки с ссылкой Пример сайта (https://example.com)

## Подзаметки (2) ##

### 1. Задачи ###
Список дел на сегодня

### 2. Идеи ###
Новые идеи для проекта`;

            results.innerHTML += '<p>✅ Экспорт данных сформирован</p>';
            results.innerHTML += '<p>✅ Ссылки обработаны правильно</p>';
            results.innerHTML += `<pre style="background-color: #1e1e1e; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;">${mockExportData}</pre>`;
        }

        function testLinkProcessing() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h4>Тест обработки ссылок:</h4>';
            
            // Симуляция обработки ссылок
            const testCases = [
                { input: 'Пример сайта', href: 'https://example.com', expected: 'Пример сайта (https://example.com)' },
                { input: 'репозиторий', href: 'https://github.com/example/repo', expected: 'репозиторий (https://github.com/example/repo)' }
            ];
            
            testCases.forEach((test, index) => {
                results.innerHTML += `<p>✅ Тест ${index + 1}: "${test.input}" → "${test.expected}"</p>`;
            });
        }

        // Автоматический тест при загрузке страницы
        window.onload = function() {
            console.log('Telegram Log Extension Test Page загружена');
            console.log('Проверьте функциональность в Telegram Web');
        };
    </script>
</body>
</html>
