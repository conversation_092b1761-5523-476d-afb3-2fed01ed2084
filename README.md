# Telegram Chat Logger

Расширение для браузера, которое добавляет дополнительные функции в веб-версию Telegram.

## Основные функции

- 📝 Логирование переписки с возможностью выбора диапазона сообщений
- 🗒️ Заметки для каждого чата
- ⚡ Быстрые сообщения (глобальные и для каждого чата)
- ✨ Улучшение текста с помощью AI
- 📋 Мультивыбор сообщений для обработки
- ⌨️ Настраиваемые горячие клавиши



## Установка

1. Клонируйте репозиторий
2. Откройте Chrome и перейдите в chrome://extensions/
3. Включите "Режим разработчика"
4. Нажмите "Загрузить распакованное расширение" и выберите папку с проектом

## Использование

После установки расширения в веб-версии Telegram появится панель с дополнительными функциями:

- **Логирование**: Выберите начало и конец диапазона сообщений для сохранения
- **Заметки**: Создавайте и сохраняйте заметки для каждого чата
- **Быстрые сообщения**: Сохраняйте часто используемые сообщения для быстрого доступа
- **Улучшение текста**: Используйте AI для улучшения ваших сообщений
- **Мультивыбор**: Выбирайте несколько сообщений для обработки (Cmd/Ctrl + клик)

## Горячие клавиши

- `Command + J` - Улучшить текст
- `Command + G` - Исправить грамматику
- `Command + K` - Поиск
- `Command + O` - Переключить фиксированное расширение заметок
- `Command + X` - Закрыть/открыть панель
- `Control + ←/→` - Переключение между папками



